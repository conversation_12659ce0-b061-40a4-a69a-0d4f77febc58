# 🤖 TKC Agents 2025

**Autonomous AI Agent Ecosystem for TKC Group**

[![Status](https://img.shields.io/badge/Status-Production-green)](https://github.com/TKCGroup/tkc-agents-2025)
[![GCP](https://img.shields.io/badge/GCP-Vertex%20AI-blue)](https://cloud.google.com/vertex-ai)
[![Python](https://img.shields.io/badge/Python-3.11+-blue)](https://python.org)
[![License](https://img.shields.io/badge/License-Private-red)](LICENSE)

A sophisticated autonomous AI agent ecosystem built on Google Cloud Platform, featuring the Aegis Trading Agent v3 for cryptocurrency market intelligence and automated business process optimization.

## 🚀 **Live Systems**

### 🤖 **Aegis Trading Agent v3** - ✅ **OPERATIONAL**
**Autonomous Cryptocurrency Research & Alert System**

- **📧 Email Alerts**: Every 4 <NAME_EMAIL>
- **🔍 Market Analysis**: 5 cryptocurrency APIs + AI insights
- **📊 Technical Analysis**: RSI, Moving Averages, Bollinger Bands
- **🛡️ Security**: Tyler-only access with complete isolation
- **☁️ Infrastructure**: GCP Cloud Run + Cloud Scheduler

**Service URL**: https://aegis-trading-agent-v3-test-7sjhmjwycq-uw.a.run.app

**📧 Tyler**: Check your email for autonomous cryptocurrency market intelligence reports!

## 🚀 **Quick Start**

### **Prerequisites**
- Google Cloud Project with billing enabled
- `gcloud` CLI installed and authenticated
- Python 3.11+
- Docker (for containerized deployment)

### **Setup**
```bash
# Clone the repository
git clone https://github.com/TKCGroup/tkc_v5.git
cd tkc_v5

# Set up GCP project and secrets
./setup_secrets.sh

# Install dependencies
pip install -r requirements.txt

# Configure environment
export GOOGLE_CLOUD_PROJECT=your-project-id
export PYTHONPATH=$(pwd)/src

# Test the agent
python src/agent/core.py
```

## 🏗️ **Architecture**

### **Core Components**
```
TKC_v5/
├── src/agent/           # LangGraph agent implementation
├── src/services/        # Gmail and external service integrations
├── src/config/          # Configuration and Secret Manager integration
├── docs/               # Comprehensive documentation
├── scripts/            # Setup and deployment automation
└── deployment/         # Docker and Cloud Run configurations
```

### **Agent Workflow**
```mermaid
graph TD
    A[User Request] --> B[Executive Agent]
    B --> C{Task Classification}
    C --> D[Email Processing]
    C --> E[General Tasks]
    D --> F[Gmail Service Agent]
    E --> G[LLM Processing]
    F --> H[Draft Creation]
    G --> I[Response Generation]
    H --> J[Result Aggregation]
    I --> J
    J --> K[User Response]
```

## 🛡️ **Security Features**

- **Zero-Trust Architecture**: Least-privilege service accounts
- **Secret Management**: GCP Secret Manager integration
- **No Hardcoded Credentials**: Application Default Credentials
- **Audit Trail**: Comprehensive logging and monitoring
- **Enterprise Compliance**: SOC2/ISO27001 ready

## 📧 **Gmail Integration**

### **Capabilities**
- ✅ **Email Draft Creation**: AI-powered email composition
- ✅ **Inbox Analysis**: Intelligent email triage and classification
- ✅ **Response Generation**: Context-aware email replies
- 🔄 **Domain Delegation**: Service account authentication (setup required)

### **Available Tools**
```python
# Create email drafts
await create_email_draft(
    to="<EMAIL>",
    subject="AI Automation Proposal",
    body="Generated by AI agent..."
)

# Analyze recent emails
emails = await get_recent_emails(query="is:unread")

# Intelligent email analysis
analysis = analyze_email_content(subject, body, sender)
```

## 🤖 **AI Agent Features**

### **LangGraph Workflow**
- **Multi-Step Reasoning**: ReAct pattern implementation
- **State Management**: Persistent conversation context
- **Tool Integration**: Extensible tool framework
- **Error Handling**: Robust error recovery and retry logic

### **Gemini Integration**
- **Model**: `gemini-2.5-flash` (optimized for speed and quality)
- **Temperature**: 0.7 (balanced creativity and consistency)
- **Context**: 8K+ token context window
- **Streaming**: Real-time response generation

## 🚀 **Deployment**

### **Local Development**
```bash
# Start the FastAPI server
python src/main.py

# Access the API
curl http://localhost:8080/health
```

### **Cloud Run Deployment**
```bash
# Build and deploy
docker build -t gcr.io/your-project/vertex-ai-agent .
docker push gcr.io/your-project/vertex-ai-agent

gcloud run deploy vertex-ai-agent \
  --image gcr.io/your-project/vertex-ai-agent \
  --platform managed \
  --region us-central1 \
  --service-account <EMAIL>
```

## 📊 **Monitoring & Observability**

- **Cloud Logging**: Structured application logs
- **Cloud Monitoring**: Performance metrics and alerts
- **Error Reporting**: Automatic error detection and notification
- **Trace**: Request tracing for performance optimization

## 📚 **Documentation**

- [**Setup Guide**](docs/setup/project-setup.md) - Complete project setup
- [**Architecture**](docs/implementation/langgraph-agent.md) - Technical architecture
- [**API Reference**](docs/implementation/api-reference.md) - API documentation
- [**Daily Logs**](docs/daily-logs/) - Development progress tracking
- [**Git Workflow**](docs/development/git-workflow.md) - Version control practices

## 🔧 **Configuration**

### **Environment Variables**
```bash
GOOGLE_CLOUD_PROJECT=vertex-ai-agent-yzdlnjey
VERTEX_AI_LOCATION=us-central1
GMAIL_SUBJECT_EMAIL=<EMAIL>
LOG_LEVEL=INFO
ENVIRONMENT=production
```

### **Secret Manager Secrets**
- `agent-config`: Agent configuration and model settings
- `gmail-config`: Gmail API configuration and scopes
- `env-variables`: Environment-specific variables
- `gmail-service-account-key`: Gmail service account credentials

## 🤝 **Contributing**

1. **Follow Git Workflow**: See [git-workflow.md](docs/development/git-workflow.md)
2. **Security First**: Never commit credentials or secrets
3. **Task Management**: Update task status and daily logs
4. **Documentation**: Update docs with any changes
5. **Testing**: Ensure all functionality works before committing

## 📈 **Roadmap**

### **Phase 1: Foundation** ✅
- [x] GCP project setup with enterprise security
- [x] LangGraph agent with Gmail integration
- [x] Configuration management with Secret Manager
- [x] Production-ready containerization

### **Phase 2: Enhancement** 🔄
- [ ] Complete Gmail domain delegation setup
- [ ] Cloud Run deployment with CI/CD
- [ ] Comprehensive monitoring and alerting
- [ ] Performance optimization and scaling

### **Phase 3: Expansion** 📋
- [ ] Calendar integration for meeting scheduling
- [ ] CRM integration for contact management
- [ ] Analytics dashboard for agent performance
- [ ] Multi-tenant architecture for enterprise use

## 📞 **Support**

- **Documentation**: [docs/](docs/)
- **Issues**: [GitHub Issues](https://github.com/TKCGroup/tkc_v5/issues)
- **Email**: <EMAIL>

---

**Built with ❤️ by TKC Group** | **Powered by Google Cloud Vertex AI**
