# Marketing Agent Production Deployment Guide

## Overview

This guide covers the deployment of the enhanced Content Marketing Agent (CMA) with comprehensive tool integrations to the production GCP environment. The agent now includes Reddit monitoring, YouTube analytics, Veo 3 video generation, Google Analytics 4 integration, and multi-platform content generation capabilities.

## Pre-Deployment Checklist

### 1. Infrastructure Requirements

#### GCP Services Required
- [x] **Vertex AI**: Gemini 2.5 Flash model access
- [x] **Cloud Functions**: Agent execution environment
- [x] **Firestore**: State and conversation storage
- [x] **Redis**: Checkpointing and session management
- [x] **Pinecone**: Vector embeddings storage
- [x] **Gmail API**: Email automation
- [ ] **Cloud Monitoring**: Performance tracking
- [ ] **Cloud Logging**: Centralized logging
- [ ] **Secret Manager**: API keys and credentials

#### External API Access
- [ ] **Reddit API**: OAuth2 application setup
- [ ] **YouTube Data API v3**: API key configuration
- [ ] **Google Analytics 4**: Property access and reporting API
- [ ] **Veo 3 API**: Vertex AI paid tier access ($0.50/second)
- [ ] **Claude 3.5 Sonnet**: Anthropic API access (optional)

### 2. Security Configuration

#### Service Account Setup
```bash
# Create marketing agent service account
gcloud iam service-accounts create marketing-agent-sa \
    --description="Service account for Content Marketing Agent" \
    --display-name="Marketing Agent SA"

# Grant necessary permissions
gcloud projects add-iam-policy-binding tkc-v5-production \
    --member="serviceAccount:<EMAIL>" \
    --role="roles/aiplatform.user"

gcloud projects add-iam-policy-binding tkc-v5-production \
    --member="serviceAccount:<EMAIL>" \
    --role="roles/datastore.user"

gcloud projects add-iam-policy-binding tkc-v5-production \
    --member="serviceAccount:<EMAIL>" \
    --role="roles/secretmanager.secretAccessor"
```

#### Secret Management
```bash
# Store API keys in Secret Manager
gcloud secrets create reddit-api-credentials --data-file=reddit_credentials.json
gcloud secrets create youtube-api-key --data-file=youtube_key.txt
gcloud secrets create ga4-credentials --data-file=ga4_service_account.json
gcloud secrets create anthropic-api-key --data-file=anthropic_key.txt

# Grant access to marketing agent
gcloud secrets add-iam-policy-binding reddit-api-credentials \
    --member="serviceAccount:<EMAIL>" \
    --role="roles/secretmanager.secretAccessor"
```

### 3. Environment Configuration

#### Production Environment Variables
```bash
export PROJECT_ID="tkc-v5-production"
export LOCATION="us-central1"
export REDIS_HOST="********"
export REDIS_PORT="6379"
export PINECONE_ENVIRONMENT="us-west1-gcp"
export GA4_PROPERTY_ID="*********"
export GEMINI_MODEL="gemini-2.5-flash"
```

#### Configuration Files
- `config/production.yaml`: Production-specific settings
- `config/api_limits.yaml`: Rate limiting configuration
- `config/compliance_rules.yaml`: Content compliance settings

## Deployment Steps

### Phase 1: Core Agent Deployment

#### 1. Deploy Base Marketing Agent
```bash
# Deploy to Cloud Functions
gcloud functions deploy content-marketing-agent \
    --source=src/Marketing-Agent \
    --entry-point=main \
    --runtime=python311 \
    --trigger=http \
    --service-account=<EMAIL> \
    --memory=2GB \
    --timeout=540s \
    --set-env-vars="PROJECT_ID=tkc-v5-production,LOCATION=us-central1"
```

#### 2. Configure Firestore Collections
```python
# Initialize required Firestore collections
collections = [
    "marketing_conversations",
    "content_campaigns", 
    "analytics_reports",
    "compliance_logs",
    "performance_metrics"
]
```

#### 3. Set Up Redis Checkpointing
```python
# Configure Redis for conversation state
redis_config = {
    "host": "********",
    "port": 6379,
    "db": 2,  # Dedicated DB for marketing agent
    "password": "secure_password"
}
```

### Phase 2: Tool Integration Deployment

#### 1. Reddit Integration
```bash
# Test Reddit API connectivity
python src/Marketing-Agent/test_reddit_integration.py

# Deploy Reddit monitoring function
gcloud functions deploy reddit-monitor \
    --source=src/Marketing-Agent/tools \
    --entry-point=monitor_reddit_conversations \
    --runtime=python311 \
    --trigger=pubsub \
    --trigger-resource=reddit-monitoring-topic
```

#### 2. YouTube Integration
```bash
# Test YouTube API connectivity
python src/Marketing-Agent/test_youtube_integration.py

# Deploy YouTube monitoring
gcloud functions deploy youtube-monitor \
    --source=src/Marketing-Agent/tools \
    --entry-point=monitor_youtube_content \
    --runtime=python311 \
    --trigger=pubsub \
    --trigger-resource=youtube-monitoring-topic
```

#### 3. Veo 3 Video Generation
```bash
# Test Veo 3 integration
python src/Marketing-Agent/test_veo3_integration.py

# Verify Vertex AI access
gcloud ai models list --region=us-central1 --filter="displayName:veo"
```

#### 4. Analytics Integration
```bash
# Test GA4 connectivity
python src/Marketing-Agent/test_analytics_integration.py

# Set up automated reporting
gcloud scheduler jobs create http ga4-daily-report \
    --schedule="0 9 * * *" \
    --uri="https://us-central1-tkc-v5-production.cloudfunctions.net/generate-marketing-dashboard" \
    --http-method=POST
```

### Phase 3: Content Generation Deployment

#### 1. Content Templates Setup
```bash
# Test content generation
python src/Marketing-Agent/test_content_generation.py

# Deploy content generation endpoint
gcloud functions deploy content-generator \
    --source=src/Marketing-Agent/tools \
    --entry-point=generate_marketing_content \
    --runtime=python311 \
    --trigger=http \
    --memory=4GB \
    --timeout=300s
```

#### 2. A/B Testing Framework
```python
# Configure A/B testing infrastructure
ab_testing_config = {
    "variation_count": 3,
    "test_duration_days": 14,
    "success_metrics": ["engagement_rate", "conversion_rate", "click_through_rate"],
    "statistical_significance": 0.95
}
```

## Monitoring and Alerting

### 1. Performance Monitoring
```bash
# Set up Cloud Monitoring dashboards
gcloud monitoring dashboards create --config-from-file=config/marketing_agent_dashboard.json

# Create alerting policies
gcloud alpha monitoring policies create --policy-from-file=config/marketing_agent_alerts.yaml
```

### 2. Key Metrics to Track
- **Response Time**: Agent response latency
- **Tool Success Rate**: Individual tool execution success
- **Content Quality Score**: Compliance and engagement metrics
- **API Rate Limits**: External API usage tracking
- **Cost Monitoring**: Veo 3 and other paid service usage

### 3. Log Analysis
```bash
# Set up log-based metrics
gcloud logging metrics create marketing_agent_errors \
    --description="Marketing agent error rate" \
    --log-filter='resource.type="cloud_function" AND resource.labels.function_name="content-marketing-agent" AND severity>=ERROR'
```

## Testing and Validation

### 1. Integration Testing
```bash
# Run comprehensive test suite
python -m pytest src/Marketing-Agent/tests/ -v

# Test all tool integrations
python src/Marketing-Agent/test_reddit_integration.py
python src/Marketing-Agent/test_youtube_integration.py
python src/Marketing-Agent/test_veo3_integration.py
python src/Marketing-Agent/test_content_generation.py
```

### 2. Load Testing
```bash
# Test agent under load
artillery run config/load_test.yml

# Monitor performance during load test
gcloud monitoring metrics list --filter="resource.type=cloud_function"
```

### 3. Compliance Validation
```python
# Test compliance checking
compliance_tests = [
    "financial_advice_detection",
    "platform_policy_compliance", 
    "content_length_validation",
    "tone_appropriateness"
]
```

## Production Rollout Strategy

### Phase 1: Limited Beta (Week 1)
- Deploy to staging environment
- Test with 10% of Reddit monitoring traffic
- Manual review of all generated content
- Monitor error rates and performance

### Phase 2: Gradual Rollout (Week 2-3)
- Increase to 50% of monitoring traffic
- Enable automated content generation for low-risk scenarios
- Implement automated compliance checking
- Set up performance dashboards

### Phase 3: Full Production (Week 4)
- 100% traffic routing to new agent
- Enable all tool integrations
- Automated content campaigns
- Full monitoring and alerting

## Rollback Plan

### Emergency Rollback
```bash
# Immediate rollback to previous version
gcloud functions deploy content-marketing-agent \
    --source=backup/Marketing-Agent-v1.0 \
    --entry-point=main \
    --runtime=python311

# Disable new tool integrations
gcloud functions delete reddit-monitor
gcloud functions delete youtube-monitor
```

### Gradual Rollback
1. Reduce traffic percentage to new agent
2. Disable specific tool integrations
3. Revert to manual content review
4. Investigate and fix issues

## Post-Deployment Tasks

### 1. Performance Optimization
- Monitor response times and optimize slow tools
- Implement caching for frequently accessed data
- Optimize Gemini model parameters based on usage

### 2. Cost Optimization
- Monitor Veo 3 usage and implement cost controls
- Optimize API call patterns to reduce costs
- Implement intelligent caching strategies

### 3. Feature Enhancement
- Collect user feedback on generated content
- Implement additional content templates
- Enhance compliance checking based on real-world usage

## Success Metrics

### Technical Metrics
- **Uptime**: >99.9% availability
- **Response Time**: <5 seconds average
- **Error Rate**: <1% of requests
- **Tool Success Rate**: >95% for each integration

### Business Metrics
- **Content Quality Score**: >8/10 average rating
- **Engagement Rate**: 20% improvement over manual content
- **Conversion Rate**: 15% improvement in lead generation
- **Cost Efficiency**: 50% reduction in content creation time

## Support and Maintenance

### 1. On-Call Procedures
- 24/7 monitoring with PagerDuty integration
- Escalation procedures for critical failures
- Automated health checks every 5 minutes

### 2. Regular Maintenance
- Weekly performance reviews
- Monthly cost analysis
- Quarterly feature updates and improvements

### 3. Documentation Updates
- Keep deployment guide current
- Update API documentation
- Maintain troubleshooting guides

## Conclusion

The enhanced Content Marketing Agent represents a significant advancement in automated marketing capabilities. With proper deployment, monitoring, and maintenance, it will provide scalable, compliant, and effective marketing automation for Bitcoin ATM customer engagement.

For questions or issues during deployment, contact the development team or refer to the troubleshooting guide in `/docs/troubleshooting.md`.
