#!/bin/bash

# Aegis Trading Agent v3 - Enhanced Intelligence System Setup
# Tyler-Only Access - Complete Isolation from Business Systems

set -e

PROJECT_ID="vertex-ai-agent-yzdlnjey"
REGION="us-west1"
DB_INSTANCE_NAME="aegis-trading-db"
DB_NAME="aegis_trading"
DB_USER="aegis_user"

echo "🚀 Setting up Aegis Trading Agent v3 Enhanced Intelligence System"
echo "=================================================="

# Set project
echo "📋 Setting GCP project to $PROJECT_ID"
gcloud config set project $PROJECT_ID

# Enable required APIs
echo "🔧 Enabling required GCP APIs..."
gcloud services enable sqladmin.googleapis.com
gcloud services enable compute.googleapis.com
gcloud services enable run.googleapis.com
gcloud services enable scheduler.googleapis.com
gcloud services enable secretmanager.googleapis.com

# Create Cloud SQL PostgreSQL instance
echo "🗄️ Creating Cloud SQL PostgreSQL instance..."
if ! gcloud sql instances describe $DB_INSTANCE_NAME --project=$PROJECT_ID >/dev/null 2>&1; then
    echo "Creating new PostgreSQL instance: $DB_INSTANCE_NAME"
    gcloud sql instances create $DB_INSTANCE_NAME \
        --database-version=POSTGRES_15 \
        --tier=db-custom-2-4096 \
        --region=$REGION \
        --storage-type=SSD \
        --storage-size=100GB \
        --storage-auto-increase \
        --backup-start-time=02:00 \
        --maintenance-window-day=SUN \
        --maintenance-window-hour=03 \
        --database-flags=shared_preload_libraries=timescaledb \
        --project=$PROJECT_ID
    
    echo "✅ PostgreSQL instance created successfully"
else
    echo "✅ PostgreSQL instance already exists"
fi

# Create database
echo "📊 Creating database: $DB_NAME"
if ! gcloud sql databases describe $DB_NAME --instance=$DB_INSTANCE_NAME --project=$PROJECT_ID >/dev/null 2>&1; then
    gcloud sql databases create $DB_NAME \
        --instance=$DB_INSTANCE_NAME \
        --project=$PROJECT_ID
    echo "✅ Database created successfully"
else
    echo "✅ Database already exists"
fi

# Create database user
echo "👤 Creating database user: $DB_USER"
DB_PASSWORD=$(openssl rand -base64 32)
if ! gcloud sql users describe $DB_USER --instance=$DB_INSTANCE_NAME --project=$PROJECT_ID >/dev/null 2>&1; then
    gcloud sql users create $DB_USER \
        --instance=$DB_INSTANCE_NAME \
        --password="$DB_PASSWORD" \
        --project=$PROJECT_ID
    echo "✅ Database user created successfully"
else
    echo "✅ Database user already exists"
    # Update password
    gcloud sql users set-password $DB_USER \
        --instance=$DB_INSTANCE_NAME \
        --password="$DB_PASSWORD" \
        --project=$PROJECT_ID
    echo "✅ Database user password updated"
fi

# Store database credentials in Secret Manager
echo "🔐 Storing database credentials in Secret Manager..."

# Database host (Cloud SQL connection name)
DB_CONNECTION_NAME=$(gcloud sql instances describe $DB_INSTANCE_NAME --project=$PROJECT_ID --format="value(connectionName)")
DB_HOST="${DB_CONNECTION_NAME}"

# Store secrets
echo "$DB_HOST" | gcloud secrets create AEGIS_TRADING_DATABASE_HOST --data-file=- --project=$PROJECT_ID || \
echo "$DB_HOST" | gcloud secrets versions add AEGIS_TRADING_DATABASE_HOST --data-file=- --project=$PROJECT_ID

echo "$DB_NAME" | gcloud secrets create AEGIS_TRADING_DATABASE_NAME --data-file=- --project=$PROJECT_ID || \
echo "$DB_NAME" | gcloud secrets versions add AEGIS_TRADING_DATABASE_NAME --data-file=- --project=$PROJECT_ID

echo "$DB_USER" | gcloud secrets create AEGIS_TRADING_DATABASE_USER --data-file=- --project=$PROJECT_ID || \
echo "$DB_USER" | gcloud secrets versions add AEGIS_TRADING_DATABASE_USER --data-file=- --project=$PROJECT_ID

echo "$DB_PASSWORD" | gcloud secrets create AEGIS_TRADING_DATABASE_PASSWORD --data-file=- --project=$PROJECT_ID || \
echo "$DB_PASSWORD" | gcloud secrets versions add AEGIS_TRADING_DATABASE_PASSWORD --data-file=- --project=$PROJECT_ID

echo "✅ Database credentials stored in Secret Manager"

# Grant service account access to secrets
SERVICE_ACCOUNT="aegis-trading-agent-sa@${PROJECT_ID}.iam.gserviceaccount.com"
echo "🔑 Granting service account access to database secrets..."

for SECRET in AEGIS_TRADING_DATABASE_HOST AEGIS_TRADING_DATABASE_NAME AEGIS_TRADING_DATABASE_USER AEGIS_TRADING_DATABASE_PASSWORD; do
    gcloud secrets add-iam-policy-binding $SECRET \
        --member="serviceAccount:$SERVICE_ACCOUNT" \
        --role="roles/secretmanager.secretAccessor" \
        --project=$PROJECT_ID
done

echo "✅ Service account granted access to database secrets"

# Initialize database schema
echo "📋 Initializing database schema..."
echo "Note: Database schema will be initialized when the application first connects"
echo "Schema file: database_schema.sql"

# Create Cloud Scheduler jobs for enhanced system
echo "⏰ Setting up enhanced Cloud Scheduler jobs..."

# Existing 4-hour analysis cycle (keep unchanged)
echo "✅ Keeping existing 4-hour analysis cycle"

# New hourly macro scan
SERVICE_URL="https://aegis-trading-agent-v3-test-7sjhmjwycq-uw.a.run.app"

if ! gcloud scheduler jobs describe aegis-macro-scan --location=$REGION --project=$PROJECT_ID >/dev/null 2>&1; then
    gcloud scheduler jobs create http aegis-macro-scan \
        --location=$REGION \
        --schedule="0 * * * *" \
        --uri="${SERVICE_URL}/run-macro-scan" \
        --http-method=GET \
        --oidc-service-account-email=$SERVICE_ACCOUNT \
        --project=$PROJECT_ID
    echo "✅ Hourly macro scan scheduler created"
else
    echo "✅ Hourly macro scan scheduler already exists"
fi

# Update configuration for enhanced features
echo "⚙️ Updating configuration for enhanced features..."

# Add enhanced configuration secrets
echo "true" | gcloud secrets create AEGIS_TRADING_ENABLE_MACRO_SCANNING --data-file=- --project=$PROJECT_ID || \
echo "true" | gcloud secrets versions add AEGIS_TRADING_ENABLE_MACRO_SCANNING --data-file=- --project=$PROJECT_ID

echo "true" | gcloud secrets create AEGIS_TRADING_ENABLE_PREDICTIVE_ANALYSIS --data-file=- --project=$PROJECT_ID || \
echo "true" | gcloud secrets versions add AEGIS_TRADING_ENABLE_PREDICTIVE_ANALYSIS --data-file=- --project=$PROJECT_ID

echo "true" | gcloud secrets create AEGIS_TRADING_ENABLE_BACKTESTING --data-file=- --project=$PROJECT_ID || \
echo "true" | gcloud secrets versions add AEGIS_TRADING_ENABLE_BACKTESTING --data-file=- --project=$PROJECT_ID

echo "100" | gcloud secrets create AEGIS_TRADING_MAX_CRYPTOCURRENCIES_MACRO_SCAN --data-file=- --project=$PROJECT_ID || \
echo "100" | gcloud secrets versions add AEGIS_TRADING_MAX_CRYPTOCURRENCIES_MACRO_SCAN --data-file=- --project=$PROJECT_ID

# Grant access to new secrets
for SECRET in AEGIS_TRADING_ENABLE_MACRO_SCANNING AEGIS_TRADING_ENABLE_PREDICTIVE_ANALYSIS AEGIS_TRADING_ENABLE_BACKTESTING AEGIS_TRADING_MAX_CRYPTOCURRENCIES_MACRO_SCAN; do
    gcloud secrets add-iam-policy-binding $SECRET \
        --member="serviceAccount:$SERVICE_ACCOUNT" \
        --role="roles/secretmanager.secretAccessor" \
        --project=$PROJECT_ID
done

echo "✅ Enhanced configuration secrets created and access granted"

# Create monitoring dashboard for enhanced system
echo "📊 Setting up enhanced monitoring..."
echo "Note: Enhanced monitoring dashboard will be created in Cloud Monitoring"

# Summary
echo ""
echo "🎉 Aegis Trading Agent v3 Enhanced Intelligence System Setup Complete!"
echo "=================================================="
echo "✅ PostgreSQL database instance created: $DB_INSTANCE_NAME"
echo "✅ Database created: $DB_NAME"
echo "✅ Database user created: $DB_USER"
echo "✅ Database credentials stored in Secret Manager"
echo "✅ Hourly macro scan scheduler created"
echo "✅ Enhanced configuration secrets created"
echo ""
echo "🔄 Next Steps:"
echo "1. Deploy enhanced application with new requirements"
echo "2. Initialize database schema on first connection"
echo "3. Verify hourly macro scans are running"
echo "4. Monitor enhanced intelligence reports"
echo ""
echo "📧 Tyler will now receive:"
echo "   • Hourly macro market intelligence (background)"
echo "   • 4-hour deep analysis reports (existing)"
echo "   • Urgent opportunity alerts (new)"
echo "   • Predictive analysis insights (new)"
echo ""
echo "🎯 Enhanced Features Enabled:"
echo "   • Macro market scanning (100+ cryptocurrencies)"
echo "   • Dynamic deep-dive research"
echo "   • Predictive pattern recognition"
echo "   • Backtesting and learning system"
echo "   • Historical data storage and analysis"
echo ""
echo "Database Connection Info:"
echo "   Host: $DB_HOST"
echo "   Database: $DB_NAME"
echo "   User: $DB_USER"
echo "   Password: [Stored in Secret Manager]"
echo ""
echo "🚀 Aegis Trading Agent v3 Enhanced Intelligence System is ready!"
