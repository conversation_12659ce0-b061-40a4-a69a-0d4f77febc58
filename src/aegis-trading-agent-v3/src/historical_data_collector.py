# Aegis Trading Agent v3 - Historical Data Collection Service
# Background service to collect and store historical cryptocurrency data
# Tyler-Only Access - Complete Isolation from Business Systems

import asyncio
from typing import List, Dict, Any, Optional
from datetime import datetime, timedelta
from decimal import Decimal
import structlog

from .config import config
from .crypto_apis import CryptoAPIManager
from .database import DatabaseManager, MarketDataRecord

logger = structlog.get_logger(__name__)

class HistoricalDataCollector:
    """Background service for collecting and storing historical cryptocurrency data."""
    
    def __init__(self):
        self.crypto_api = CryptoAPIManager()
        self.db_manager = DatabaseManager()
        self.is_running = False
        
        # Target cryptocurrencies for historical data collection
        self.target_cryptocurrencies = [
            # Core holdings (existing 9)
            "bitcoin", "ethereum", "solana", "cardano", "polkadot", 
            "chainlink", "avalanche-2", "polygon", "cosmos", "near",
            
            # Top market cap additions
            "binancecoin", "ripple", "dogecoin", "tron", "litecoin",
            "shiba-inu", "uniswap", "chainlink", "polygon", "internet-computer",
            
            # DeFi tokens
            "aave", "compound-governance-token", "maker", "curve-dao-token",
            "sushiswap", "pancakeswap-token", "1inch", "yearn-finance",
            
            # Layer 1 alternatives
            "algorand", "tezos", "elrond-erd-2", "fantom", "harmony",
            "theta-token", "hedera-hashgraph", "eos", "iota", "vechain",
            
            # Gaming/NFT
            "axie-infinity", "the-sandbox", "decentraland", "enjincoin",
            "gala", "immutable-x", "stepn", "flow",
            
            # AI/Data
            "fetch-ai", "singularitynet", "ocean-protocol", "numeraire",
            "cortex", "matrix-ai-network", "render-token",
            
            # Infrastructure
            "filecoin", "arweave", "helium", "the-graph", "basic-attention-token",
            
            # Privacy
            "monero", "zcash", "dash", "horizen", "beam",
            
            # Stablecoins (for reference)
            "tether", "usd-coin", "binance-usd", "dai", "frax"
        ]
        
        logger.info(f"Historical data collector initialized for {len(self.target_cryptocurrencies)} cryptocurrencies")
    
    async def start_collection(self, backfill_days: int = 30) -> bool:
        """Start historical data collection process."""
        try:
            logger.info(f"Starting historical data collection with {backfill_days} days backfill")
            
            # Initialize database connection
            await self.db_manager.initialize()
            
            # Collect initial historical data (backfill)
            await self._collect_historical_backfill(backfill_days)
            
            # Start continuous collection loop
            self.is_running = True
            await self._continuous_collection_loop()
            
            return True
            
        except Exception as e:
            logger.error(f"Failed to start historical data collection: {str(e)}")
            return False
    
    async def stop_collection(self):
        """Stop the historical data collection process."""
        self.is_running = False
        await self.db_manager.close()
        logger.info("Historical data collection stopped")
    
    async def _collect_historical_backfill(self, days: int):
        """Collect historical data for the specified number of days."""
        logger.info(f"Starting historical backfill for {days} days")
        
        try:
            # Calculate date range
            end_date = datetime.utcnow()
            start_date = end_date - timedelta(days=days)
            
            # Collect data in chunks to avoid API rate limits
            chunk_size = 10  # Process 10 cryptocurrencies at a time
            
            for i in range(0, len(self.target_cryptocurrencies), chunk_size):
                chunk = self.target_cryptocurrencies[i:i + chunk_size]
                logger.info(f"Processing chunk {i//chunk_size + 1}: {chunk}")
                
                # Collect data for this chunk
                await self._collect_chunk_historical_data(chunk, start_date, end_date)
                
                # Rate limiting - wait between chunks
                await asyncio.sleep(2)
            
            logger.info("Historical backfill completed successfully")
            
        except Exception as e:
            logger.error(f"Historical backfill failed: {str(e)}")
            raise
    
    async def _collect_chunk_historical_data(self, symbols: List[str], start_date: datetime, end_date: datetime):
        """Collect historical data for a chunk of cryptocurrencies."""
        try:
            # Use CoinGecko for historical data
            if self.crypto_api.coingecko:
                for symbol in symbols:
                    try:
                        # Get historical data from CoinGecko
                        historical_data = await self._get_coingecko_historical_data(
                            symbol, start_date, end_date
                        )
                        
                        if historical_data:
                            # Store in database
                            await self.db_manager.store_market_data(historical_data)
                            logger.info(f"Stored {len(historical_data)} historical records for {symbol}")
                        
                        # Rate limiting between symbols
                        await asyncio.sleep(0.5)
                        
                    except Exception as e:
                        logger.warning(f"Failed to collect historical data for {symbol}: {str(e)}")
                        continue
            
        except Exception as e:
            logger.error(f"Failed to collect chunk historical data: {str(e)}")
            raise
    
    async def _get_coingecko_historical_data(self, symbol: str, start_date: datetime, end_date: datetime) -> List[MarketDataRecord]:
        """Get historical data from CoinGecko API."""
        try:
            import aiohttp
            
            # CoinGecko historical data endpoint
            base_url = "https://api.coingecko.com/api/v3"
            
            # Calculate days difference
            days = (end_date - start_date).days
            
            async with aiohttp.ClientSession() as session:
                url = f"{base_url}/coins/{symbol}/market_chart"
                params = {
                    "vs_currency": "usd",
                    "days": days,
                    "interval": "hourly" if days <= 90 else "daily"
                }
                
                async with session.get(url, params=params) as response:
                    if response.status == 200:
                        data = await response.json()
                        
                        # Parse the response
                        prices = data.get("prices", [])
                        volumes = data.get("total_volumes", [])
                        market_caps = data.get("market_caps", [])
                        
                        # Create market data records
                        records = []
                        for i, (timestamp_ms, price) in enumerate(prices):
                            timestamp = datetime.fromtimestamp(timestamp_ms / 1000)
                            
                            # Get corresponding volume and market cap
                            volume = volumes[i][1] if i < len(volumes) else 0
                            market_cap = market_caps[i][1] if i < len(market_caps) else 0
                            
                            # Calculate price change (if we have previous price)
                            price_change_24h = None
                            if i > 0:
                                prev_price = prices[i-1][1]
                                if prev_price > 0:
                                    price_change_24h = ((price - prev_price) / prev_price) * 100
                            
                            record = MarketDataRecord(
                                symbol=symbol,
                                price=Decimal(str(price)),
                                volume_24h=int(volume) if volume else None,
                                market_cap=int(market_cap) if market_cap else None,
                                price_change_24h=Decimal(str(price_change_24h)) if price_change_24h else None,
                                timestamp=timestamp
                            )
                            records.append(record)
                        
                        return records
                    else:
                        logger.warning(f"CoinGecko API error for {symbol}: {response.status}")
                        return []
        
        except Exception as e:
            logger.error(f"Failed to get CoinGecko historical data for {symbol}: {str(e)}")
            return []
    
    async def _continuous_collection_loop(self):
        """Continuous loop for collecting real-time market data."""
        logger.info("Starting continuous data collection loop")
        
        while self.is_running:
            try:
                # Collect current market data
                await self._collect_current_market_data()
                
                # Wait for next collection cycle (every 5 minutes)
                await asyncio.sleep(300)  # 5 minutes
                
            except Exception as e:
                logger.error(f"Error in continuous collection loop: {str(e)}")
                # Wait before retrying
                await asyncio.sleep(60)  # 1 minute
    
    async def _collect_current_market_data(self):
        """Collect current market data for all target cryptocurrencies."""
        try:
            logger.info("Collecting current market data")
            
            # Get current market data from CoinGecko
            if self.crypto_api.coingecko:
                current_data = await self.crypto_api.coingecko.get_market_data(
                    self.target_cryptocurrencies
                )
                
                if current_data:
                    # Convert to MarketDataRecord format
                    records = []
                    for coin_data in current_data:
                        record = MarketDataRecord(
                            symbol=coin_data.symbol,
                            price=coin_data.price,
                            volume_24h=coin_data.volume_24h,
                            market_cap=coin_data.market_cap,
                            price_change_24h=coin_data.price_change_24h,
                            timestamp=datetime.utcnow()
                        )
                        records.append(record)
                    
                    # Store in database
                    await self.db_manager.store_market_data(records)
                    logger.info(f"Stored {len(records)} current market data records")
                
        except Exception as e:
            logger.error(f"Failed to collect current market data: {str(e)}")
    
    async def get_collection_status(self) -> Dict[str, Any]:
        """Get status of the historical data collection."""
        try:
            # Get database statistics
            stats = await self.db_manager.get_market_data_statistics()
            
            return {
                "is_running": self.is_running,
                "target_cryptocurrencies": len(self.target_cryptocurrencies),
                "database_stats": stats,
                "last_update": datetime.utcnow().isoformat()
            }
            
        except Exception as e:
            logger.error(f"Failed to get collection status: {str(e)}")
            return {
                "is_running": self.is_running,
                "error": str(e)
            }

# Background task runner
async def run_historical_data_collection(backfill_days: int = 30):
    """Run the historical data collection service."""
    collector = HistoricalDataCollector()
    
    try:
        await collector.start_collection(backfill_days)
    except KeyboardInterrupt:
        logger.info("Received interrupt signal, stopping collection...")
    except Exception as e:
        logger.error(f"Historical data collection failed: {str(e)}")
    finally:
        await collector.stop_collection()

if __name__ == "__main__":
    # Run historical data collection
    asyncio.run(run_historical_data_collection())
