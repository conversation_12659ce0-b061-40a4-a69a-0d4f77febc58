# Aegis Trading Agent v3 - Macro Market Scanner
# Hourly market-wide cryptocurrency analysis and opportunity detection
# Tyler-Only Access - Complete Isolation from Business Systems

import asyncio
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime, timedelta
from dataclasses import dataclass
from decimal import Decimal
import structlog

from .config import config
from .crypto_apis import CryptoAPIManager
from .database import MacroScanResult, store_macro_scan_result

logger = structlog.get_logger(__name__)

# =====================================================
# SECTOR DEFINITIONS
# =====================================================

CRYPTO_SECTORS = {
    "Layer1": ["bitcoin", "ethereum", "solana", "cardano", "avalanche-2", "polkadot", "cosmos", "near", "algorand", "tezos"],
    "DeFi": ["uniswap", "aave", "compound-governance-token", "maker", "curve-dao-token", "sushiswap", "pancakeswap-token", "1inch"],
    "Gaming": ["axie-infinity", "the-sandbox", "decentraland", "enjincoin", "gala", "immutable-x", "stepn"],
    "AI": ["fetch-ai", "singularitynet", "ocean-protocol", "numeraire", "cortex", "matrix-ai-network"],
    "Meme": ["dogecoin", "shiba-inu", "pepe", "floki", "bonk", "dogwifcoin"],
    "Infrastructure": ["chainlink", "the-graph", "filecoin", "arweave", "helium", "render-token"],
    "Privacy": ["monero", "zcash", "dash", "horizen", "beam", "grin"],
    "Exchange": ["binancecoin", "crypto-com-chain", "ftx-token", "kucoin-shares", "huobi-token"],
    "Stablecoins": ["tether", "usd-coin", "binance-usd", "dai", "frax", "terrausd"]
}

@dataclass
class OpportunitySignal:
    """Represents a detected market opportunity."""
    symbol: str
    signal_type: str  # 'volume_spike', 'price_breakout', 'sector_momentum', 'whale_movement'
    confidence_score: float
    current_price: Decimal
    price_change_24h: float
    volume_change_24h: float
    market_cap_rank: Optional[int]
    reasoning: str
    sector: Optional[str]
    urgency: str  # 'low', 'medium', 'high', 'urgent'

@dataclass
class SectorAnalysis:
    """Sector performance analysis."""
    sector_name: str
    total_market_cap: int
    performance_24h: float
    performance_7d: float
    volume_24h: int
    top_performers: List[Dict[str, Any]]
    opportunity_score: float
    trend_direction: str  # 'bullish', 'bearish', 'neutral'

# =====================================================
# MACRO MARKET SCANNER
# =====================================================

class MacroMarketScanner:
    """Hourly macro market scanner for comprehensive cryptocurrency analysis."""
    
    def __init__(self):
        self.crypto_api = CryptoAPIManager()
        self.scan_start_time = None
        
        logger.info("MacroMarketScanner initialized")
    
    async def run_macro_scan(self) -> MacroScanResult:
        """Run comprehensive macro market scan."""
        self.scan_start_time = datetime.utcnow()
        logger.info("Starting macro market scan")
        
        try:
            # 1. Get market overview data
            market_overview = await self._get_market_overview()
            
            # 2. Analyze sectors
            sector_analysis = await self._analyze_sectors()
            
            # 3. Detect opportunities
            opportunities = await self._detect_opportunities(market_overview)
            
            # 4. Identify volume spikes
            volume_spikes = await self._detect_volume_spikes(market_overview)
            
            # 5. Calculate overall opportunity score
            opportunity_score = self._calculate_opportunity_score(
                market_overview, sector_analysis, opportunities
            )
            
            # 6. Determine triggers for deep analysis
            triggers_activated = self._determine_analysis_triggers(
                opportunity_score, opportunities, sector_analysis
            )
            
            # 7. Create scan result
            execution_time = (datetime.utcnow() - self.scan_start_time).total_seconds()
            
            scan_result = MacroScanResult(
                scan_type="hourly_macro",
                total_cryptos_analyzed=len(market_overview.get("cryptocurrencies", [])),
                market_summary=self._create_market_summary(market_overview),
                sector_analysis=sector_analysis,
                top_gainers=market_overview.get("top_gainers", []),
                top_losers=market_overview.get("top_losers", []),
                volume_spikes=volume_spikes,
                opportunity_score=Decimal(str(opportunity_score)),
                opportunities_detected=len(opportunities),
                triggers_activated=triggers_activated,
                execution_time_seconds=Decimal(str(execution_time)),
                timestamp=self.scan_start_time
            )
            
            # 8. Store results in database
            await store_macro_scan_result(scan_result)
            
            logger.info(f"Macro scan completed: {len(opportunities)} opportunities detected, "
                       f"opportunity score: {opportunity_score:.3f}")
            
            return scan_result
            
        except Exception as e:
            logger.error(f"Error in macro market scan: {str(e)}")
            raise
    
    async def _get_market_overview(self) -> Dict[str, Any]:
        """Get comprehensive market overview data."""
        try:
            # Get top cryptocurrencies by market cap
            if self.crypto_api.coingecko:
                # Use CoinGecko's markets endpoint for comprehensive data
                import aiohttp
                
                async with aiohttp.ClientSession() as session:
                    url = f"{self.crypto_api.coingecko.base_url}/coins/markets"
                    params = {
                        "vs_currency": "usd",
                        "order": "market_cap_desc",
                        "per_page": config.max_cryptocurrencies_macro_scan,
                        "page": 1,
                        "sparkline": False,
                        "price_change_percentage": "1h,24h,7d"
                    }
                    
                    async with session.get(url, params=params) as response:
                        if response.status == 200:
                            data = await response.json()
                            
                            # Process and categorize data
                            cryptocurrencies = []
                            top_gainers = []
                            top_losers = []
                            
                            for coin in data:
                                crypto_data = {
                                    "symbol": coin["symbol"],
                                    "name": coin["name"],
                                    "current_price": coin["current_price"],
                                    "market_cap": coin["market_cap"],
                                    "market_cap_rank": coin["market_cap_rank"],
                                    "price_change_24h": coin.get("price_change_percentage_24h", 0),
                                    "price_change_7d": coin.get("price_change_percentage_7d", 0),
                                    "volume_24h": coin["total_volume"],
                                    "circulating_supply": coin["circulating_supply"],
                                    "total_supply": coin["total_supply"],
                                    "max_supply": coin["max_supply"]
                                }
                                cryptocurrencies.append(crypto_data)
                                
                                # Categorize gainers and losers
                                price_change = coin.get("price_change_percentage_24h", 0)
                                if price_change > 10:  # 10%+ gain
                                    top_gainers.append(crypto_data)
                                elif price_change < -10:  # 10%+ loss
                                    top_losers.append(crypto_data)
                            
                            # Sort gainers and losers
                            top_gainers.sort(key=lambda x: x["price_change_24h"], reverse=True)
                            top_losers.sort(key=lambda x: x["price_change_24h"])
                            
                            return {
                                "cryptocurrencies": cryptocurrencies,
                                "top_gainers": top_gainers[:10],
                                "top_losers": top_losers[:10],
                                "total_market_cap": sum(c["market_cap"] for c in cryptocurrencies if c["market_cap"]),
                                "total_volume_24h": sum(c["volume_24h"] for c in cryptocurrencies if c["volume_24h"])
                            }
                        else:
                            logger.error(f"CoinGecko markets API error: {response.status}")
                            return {"cryptocurrencies": [], "top_gainers": [], "top_losers": []}
            
            return {"cryptocurrencies": [], "top_gainers": [], "top_losers": []}
            
        except Exception as e:
            logger.error(f"Error getting market overview: {str(e)}")
            return {"cryptocurrencies": [], "top_gainers": [], "top_losers": []}
    
    async def _analyze_sectors(self) -> Dict[str, SectorAnalysis]:
        """Analyze performance of different cryptocurrency sectors."""
        sector_analyses = {}
        
        try:
            for sector_name, symbols in CRYPTO_SECTORS.items():
                # Get market data for sector symbols
                if self.crypto_api.coingecko:
                    sector_data = await self.crypto_api.coingecko.get_market_data(symbols[:10])  # Limit to top 10 per sector
                    
                    if sector_data:
                        # Calculate sector metrics
                        total_market_cap = sum(coin.market_cap for coin in sector_data if coin.market_cap)
                        total_volume = sum(coin.volume_24h for coin in sector_data if coin.volume_24h)
                        
                        # Calculate weighted average performance
                        weighted_performance_24h = 0
                        total_weight = 0
                        
                        top_performers = []
                        
                        for coin in sector_data:
                            if coin.market_cap and coin.price_change_24h is not None:
                                weight = coin.market_cap
                                weighted_performance_24h += coin.price_change_24h * weight
                                total_weight += weight
                                
                                if coin.price_change_24h > 5:  # 5%+ gain
                                    top_performers.append({
                                        "symbol": coin.symbol,
                                        "price_change_24h": coin.price_change_24h,
                                        "market_cap": coin.market_cap
                                    })
                        
                        if total_weight > 0:
                            avg_performance = weighted_performance_24h / total_weight
                        else:
                            avg_performance = 0
                        
                        # Calculate opportunity score for sector
                        opportunity_score = self._calculate_sector_opportunity_score(
                            avg_performance, len(top_performers), total_volume
                        )
                        
                        # Determine trend direction
                        if avg_performance > 5:
                            trend_direction = "bullish"
                        elif avg_performance < -5:
                            trend_direction = "bearish"
                        else:
                            trend_direction = "neutral"
                        
                        sector_analyses[sector_name] = SectorAnalysis(
                            sector_name=sector_name,
                            total_market_cap=total_market_cap,
                            performance_24h=avg_performance,
                            performance_7d=0,  # TODO: Calculate 7d performance
                            volume_24h=total_volume,
                            top_performers=top_performers[:5],
                            opportunity_score=opportunity_score,
                            trend_direction=trend_direction
                        )
            
            logger.info(f"Analyzed {len(sector_analyses)} cryptocurrency sectors")
            return sector_analyses
            
        except Exception as e:
            logger.error(f"Error analyzing sectors: {str(e)}")
            return {}
    
    async def _detect_opportunities(self, market_overview: Dict[str, Any]) -> List[OpportunitySignal]:
        """Detect market opportunities based on various signals."""
        opportunities = []
        
        try:
            cryptocurrencies = market_overview.get("cryptocurrencies", [])
            
            for crypto in cryptocurrencies:
                signals = []
                
                # Volume spike detection
                if crypto.get("volume_24h", 0) > 0:
                    # TODO: Compare with historical volume (requires database)
                    # For now, use market cap ratio as proxy
                    volume_to_mcap_ratio = crypto["volume_24h"] / max(crypto.get("market_cap", 1), 1)
                    if volume_to_mcap_ratio > 0.3:  # High volume relative to market cap
                        signals.append("volume_spike")
                
                # Price breakout detection
                price_change = crypto.get("price_change_24h", 0)
                if price_change > 15:  # 15%+ gain
                    signals.append("price_breakout")
                elif price_change < -15:  # 15%+ drop (potential buy opportunity)
                    signals.append("oversold_bounce")
                
                # Market cap rank improvement (requires historical data)
                # TODO: Implement when database is available
                
                # Create opportunity signals
                for signal_type in signals:
                    confidence_score = self._calculate_signal_confidence(crypto, signal_type)
                    
                    if confidence_score >= config.opportunity_score_threshold:
                        # Determine sector
                        sector = self._get_crypto_sector(crypto["symbol"])
                        
                        # Determine urgency
                        urgency = self._determine_urgency(confidence_score, price_change)
                        
                        opportunity = OpportunitySignal(
                            symbol=crypto["symbol"],
                            signal_type=signal_type,
                            confidence_score=confidence_score,
                            current_price=Decimal(str(crypto["current_price"])),
                            price_change_24h=price_change,
                            volume_change_24h=0,  # TODO: Calculate from historical data
                            market_cap_rank=crypto.get("market_cap_rank"),
                            reasoning=self._generate_opportunity_reasoning(crypto, signal_type),
                            sector=sector,
                            urgency=urgency
                        )
                        
                        opportunities.append(opportunity)
            
            # Sort by confidence score
            opportunities.sort(key=lambda x: x.confidence_score, reverse=True)
            
            logger.info(f"Detected {len(opportunities)} market opportunities")
            return opportunities[:20]  # Return top 20 opportunities
            
        except Exception as e:
            logger.error(f"Error detecting opportunities: {str(e)}")
            return []
    
    def _calculate_opportunity_score(self, market_overview: Dict[str, Any], 
                                   sector_analysis: Dict[str, SectorAnalysis], 
                                   opportunities: List[OpportunitySignal]) -> float:
        """Calculate overall market opportunity score (0-1)."""
        try:
            # Base score from number of opportunities
            opportunity_factor = min(len(opportunities) / 10, 1.0)  # Normalize to 0-1
            
            # Sector momentum factor
            bullish_sectors = sum(1 for s in sector_analysis.values() if s.trend_direction == "bullish")
            sector_factor = bullish_sectors / max(len(sector_analysis), 1)
            
            # High confidence opportunities factor
            high_conf_opportunities = sum(1 for o in opportunities if o.confidence_score > 0.8)
            confidence_factor = min(high_conf_opportunities / 5, 1.0)
            
            # Weighted average
            overall_score = (
                opportunity_factor * 0.4 +
                sector_factor * 0.3 +
                confidence_factor * 0.3
            )
            
            return min(overall_score, 1.0)
            
        except Exception as e:
            logger.error(f"Error calculating opportunity score: {str(e)}")
            return 0.0
    
    def _determine_analysis_triggers(self, opportunity_score: float, 
                                   opportunities: List[OpportunitySignal],
                                   sector_analysis: Dict[str, SectorAnalysis]) -> Dict[str, Any]:
        """Determine which deep analysis triggers should be activated."""
        triggers = {
            "deep_analysis_triggered": False,
            "urgent_analysis_triggered": False,
            "sector_analysis_triggered": [],
            "individual_crypto_analysis": [],
            "reasoning": []
        }
        
        try:
            # Trigger deep analysis if opportunity score is high
            if opportunity_score >= config.opportunity_score_threshold:
                triggers["deep_analysis_triggered"] = True
                triggers["reasoning"].append(f"High opportunity score: {opportunity_score:.3f}")
            
            # Trigger urgent analysis for high-confidence, urgent opportunities
            urgent_opportunities = [o for o in opportunities if o.urgency == "urgent" and o.confidence_score > 0.8]
            if urgent_opportunities:
                triggers["urgent_analysis_triggered"] = True
                triggers["individual_crypto_analysis"] = [o.symbol for o in urgent_opportunities[:5]]
                triggers["reasoning"].append(f"Urgent opportunities detected: {len(urgent_opportunities)}")
            
            # Trigger sector analysis for bullish sectors
            bullish_sectors = [s.sector_name for s in sector_analysis.values() 
                             if s.trend_direction == "bullish" and s.opportunity_score > 0.7]
            if bullish_sectors:
                triggers["sector_analysis_triggered"] = bullish_sectors
                triggers["reasoning"].append(f"Bullish sectors: {', '.join(bullish_sectors)}")
            
            return triggers
            
        except Exception as e:
            logger.error(f"Error determining analysis triggers: {str(e)}")
            return triggers
    
    # Helper methods
    def _calculate_sector_opportunity_score(self, performance: float, top_performers: int, volume: int) -> float:
        """Calculate opportunity score for a sector."""
        # Simple scoring based on performance and activity
        performance_score = min(max(performance / 20, 0), 1)  # Normalize -20% to +20% to 0-1
        activity_score = min(top_performers / 5, 1)  # Up to 5 top performers
        volume_score = min(volume / 1e9, 1)  # Normalize volume (1B+ = 1.0)
        
        return (performance_score * 0.5 + activity_score * 0.3 + volume_score * 0.2)
    
    def _calculate_signal_confidence(self, crypto: Dict[str, Any], signal_type: str) -> float:
        """Calculate confidence score for a signal."""
        base_confidence = 0.5
        
        # Adjust based on market cap rank (higher rank = higher confidence)
        rank = crypto.get("market_cap_rank", 1000)
        rank_factor = max(0, 1 - (rank / 100))  # Top 100 get bonus
        
        # Adjust based on price change magnitude
        price_change = abs(crypto.get("price_change_24h", 0))
        price_factor = min(price_change / 20, 1)  # 20%+ change = max factor
        
        confidence = base_confidence + (rank_factor * 0.3) + (price_factor * 0.2)
        return min(confidence, 1.0)
    
    def _get_crypto_sector(self, symbol: str) -> Optional[str]:
        """Get sector for a cryptocurrency symbol."""
        for sector, symbols in CRYPTO_SECTORS.items():
            if symbol in symbols:
                return sector
        return None
    
    def _determine_urgency(self, confidence_score: float, price_change: float) -> str:
        """Determine urgency level for an opportunity."""
        if confidence_score > 0.9 and abs(price_change) > 20:
            return "urgent"
        elif confidence_score > 0.8 and abs(price_change) > 15:
            return "high"
        elif confidence_score > 0.7:
            return "medium"
        else:
            return "low"
    
    def _generate_opportunity_reasoning(self, crypto: Dict[str, Any], signal_type: str) -> str:
        """Generate reasoning for an opportunity."""
        symbol = crypto["symbol"].upper()
        price_change = crypto.get("price_change_24h", 0)
        
        if signal_type == "volume_spike":
            return f"{symbol} showing unusual volume activity with {price_change:+.1f}% price change"
        elif signal_type == "price_breakout":
            return f"{symbol} breaking out with {price_change:+.1f}% gain in 24h"
        elif signal_type == "oversold_bounce":
            return f"{symbol} potentially oversold with {price_change:+.1f}% drop, possible bounce opportunity"
        else:
            return f"{symbol} showing {signal_type} signal with {price_change:+.1f}% price movement"
    
    async def _detect_volume_spikes(self, market_overview: Dict[str, Any]) -> Dict[str, Any]:
        """Detect unusual volume activity."""
        volume_spikes = []
        
        try:
            cryptocurrencies = market_overview.get("cryptocurrencies", [])
            
            # Calculate average volume for comparison
            volumes = [c.get("volume_24h", 0) for c in cryptocurrencies if c.get("volume_24h")]
            if volumes:
                avg_volume = sum(volumes) / len(volumes)
                
                for crypto in cryptocurrencies:
                    volume = crypto.get("volume_24h", 0)
                    if volume > avg_volume * 3:  # 3x average volume
                        volume_spikes.append({
                            "symbol": crypto["symbol"],
                            "volume_24h": volume,
                            "volume_ratio": volume / avg_volume,
                            "price_change_24h": crypto.get("price_change_24h", 0)
                        })
                
                # Sort by volume ratio
                volume_spikes.sort(key=lambda x: x["volume_ratio"], reverse=True)
            
            return {"spikes": volume_spikes[:10], "average_volume": avg_volume if volumes else 0}
            
        except Exception as e:
            logger.error(f"Error detecting volume spikes: {str(e)}")
            return {"spikes": [], "average_volume": 0}
    
    def _create_market_summary(self, market_overview: Dict[str, Any]) -> Dict[str, Any]:
        """Create market summary from overview data."""
        cryptocurrencies = market_overview.get("cryptocurrencies", [])
        
        if not cryptocurrencies:
            return {"total_cryptos": 0, "market_status": "no_data"}
        
        # Calculate market metrics
        positive_changes = sum(1 for c in cryptocurrencies if c.get("price_change_24h", 0) > 0)
        negative_changes = sum(1 for c in cryptocurrencies if c.get("price_change_24h", 0) < 0)
        
        avg_change = sum(c.get("price_change_24h", 0) for c in cryptocurrencies) / len(cryptocurrencies)
        
        # Determine market sentiment
        if avg_change > 2:
            market_sentiment = "bullish"
        elif avg_change < -2:
            market_sentiment = "bearish"
        else:
            market_sentiment = "neutral"
        
        return {
            "total_cryptos": len(cryptocurrencies),
            "total_market_cap": market_overview.get("total_market_cap", 0),
            "total_volume_24h": market_overview.get("total_volume_24h", 0),
            "positive_changes": positive_changes,
            "negative_changes": negative_changes,
            "average_change_24h": avg_change,
            "market_sentiment": market_sentiment,
            "top_gainers_count": len(market_overview.get("top_gainers", [])),
            "top_losers_count": len(market_overview.get("top_losers", []))
        }
