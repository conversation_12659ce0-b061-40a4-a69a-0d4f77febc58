# Aegis Trading Agent v3 - Database Initialization
# Initialize PostgreSQL database schema with TimescaleDB extensions
# Tyler-Only Access - Complete Isolation from Business Systems

import asyncio
import asyncpg
from typing import Optional
import structlog
from pathlib import Path

from .config import config

logger = structlog.get_logger(__name__)

class DatabaseInitializer:
    """Initialize database schema and TimescaleDB extensions."""
    
    def __init__(self):
        self.connection: Optional[asyncpg.Connection] = None
        
    async def connect(self) -> bool:
        """Connect to the database."""
        try:
            # Get database configuration from secrets
            from google.cloud import secretmanager
            client = secretmanager.SecretManagerServiceClient()
            project_id = "vertex-ai-agent-yzdlnjey"
            
            # Retrieve database credentials
            host_secret = client.access_secret_version(
                request={"name": f"projects/{project_id}/secrets/AEGIS_TRADING_DATABASE_HOST/versions/latest"}
            )
            db_host = host_secret.payload.data.decode("UTF-8").strip()

            name_secret = client.access_secret_version(
                request={"name": f"projects/{project_id}/secrets/AEGIS_TRADING_DATABASE_NAME/versions/latest"}
            )
            db_name = name_secret.payload.data.decode("UTF-8").strip()

            user_secret = client.access_secret_version(
                request={"name": f"projects/{project_id}/secrets/AEGIS_TRADING_DATABASE_USER/versions/latest"}
            )
            db_user = user_secret.payload.data.decode("UTF-8").strip()

            password_secret = client.access_secret_version(
                request={"name": f"projects/{project_id}/secrets/AEGIS_TRADING_DATABASE_PASSWORD/versions/latest"}
            )
            db_password = password_secret.payload.data.decode("UTF-8").strip()
            
            # Connect to database using the host from secrets
            # For Cloud Run + Cloud SQL, this will be the Unix socket path
            if db_host.startswith("/cloudsql/"):
                # Unix socket connection for Cloud SQL
                self.connection = await asyncpg.connect(
                    host=db_host,
                    database=db_name,
                    user=db_user,
                    password=db_password
                )
            else:
                # TCP connection (for local development or external access)
                self.connection = await asyncpg.connect(
                    host=db_host,
                    port=5432,
                    database=db_name,
                    user=db_user,
                    password=db_password,
                    ssl="require"
                )
            
            logger.info("Successfully connected to PostgreSQL database")
            return True
            
        except Exception as e:
            logger.error(f"Failed to connect to database: {str(e)}")
            return False
    
    async def initialize_schema(self) -> bool:
        """Initialize the complete database schema."""
        if not self.connection:
            logger.error("No database connection available")
            return False
        
        try:
            # Read the schema file
            schema_file = Path(__file__).parent.parent / "database_schema.sql"
            if not schema_file.exists():
                logger.error(f"Schema file not found: {schema_file}")
                return False
            
            schema_sql = schema_file.read_text()
            
            # Execute the schema in a transaction
            async with self.connection.transaction():
                # Split the schema into individual statements
                statements = [stmt.strip() for stmt in schema_sql.split(';') if stmt.strip()]
                
                for i, statement in enumerate(statements):
                    try:
                        logger.info(f"Executing statement {i+1}/{len(statements)}")
                        await self.connection.execute(statement)
                    except Exception as e:
                        # Some statements might fail if already exist, that's okay
                        if "already exists" in str(e).lower():
                            logger.info(f"Statement {i+1} skipped (already exists): {str(e)}")
                        else:
                            logger.warning(f"Statement {i+1} failed: {str(e)}")
                            # Continue with other statements
            
            logger.info("Database schema initialization completed successfully")
            return True
            
        except Exception as e:
            logger.error(f"Failed to initialize database schema: {str(e)}")
            return False
    
    async def verify_schema(self) -> bool:
        """Verify that all required tables exist."""
        if not self.connection:
            return False
        
        try:
            # Check for required tables
            required_tables = [
                'market_data_history',
                'macro_scan_results', 
                'analysis_results',
                'predictions',
                'backtesting_results',
                'sector_analysis',
                'pattern_recognition',
                'system_performance'
            ]
            
            for table in required_tables:
                result = await self.connection.fetchval(
                    "SELECT EXISTS (SELECT FROM information_schema.tables WHERE table_name = $1)",
                    table
                )
                if not result:
                    logger.error(f"Required table '{table}' does not exist")
                    return False
                else:
                    logger.info(f"Table '{table}' verified")
            
            # Check for TimescaleDB extension
            result = await self.connection.fetchval(
                "SELECT EXISTS (SELECT FROM pg_extension WHERE extname = 'timescaledb')"
            )
            if result:
                logger.info("TimescaleDB extension is available")
            else:
                logger.warning("TimescaleDB extension not found - time-series optimization disabled")
            
            logger.info("Database schema verification completed successfully")
            return True
            
        except Exception as e:
            logger.error(f"Failed to verify database schema: {str(e)}")
            return False
    
    async def close(self):
        """Close database connection."""
        if self.connection:
            await self.connection.close()
            self.connection = None

async def initialize_database() -> bool:
    """Initialize the database schema if needed."""
    initializer = DatabaseInitializer()
    
    try:
        # Connect to database
        if not await initializer.connect():
            return False
        
        # Initialize schema
        if not await initializer.initialize_schema():
            return False
        
        # Verify schema
        if not await initializer.verify_schema():
            return False
        
        logger.info("Database initialization completed successfully")
        return True
        
    except Exception as e:
        logger.error(f"Database initialization failed: {str(e)}")
        return False
        
    finally:
        await initializer.close()

# Test function for manual verification
async def test_database_connection():
    """Test database connection and schema."""
    logger.info("Testing database connection and schema...")
    
    success = await initialize_database()
    if success:
        logger.info("✅ Database test completed successfully")
    else:
        logger.error("❌ Database test failed")
    
    return success

if __name__ == "__main__":
    # Run database initialization test
    asyncio.run(test_database_connection())
