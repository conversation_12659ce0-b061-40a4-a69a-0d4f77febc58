# Aegis Trading Agent v3 - Email Alert System
# Autonomous Cryptocurrency Research & Alert System

import base64
import json
from email.mime.text import MIMEText
from email.mime.multipart import MI<PERSON><PERSON><PERSON>ipart
from typing import List, Dict, Any, Optional
from datetime import datetime
import structlog

from googleapiclient.discovery import build
from google.oauth2.service_account import Credentials
from google.cloud import secretmanager

from .config import config
from .analysis import TradingSignal, MarketIntelligenceReport, SignalType, ConfidenceLevel

logger = structlog.get_logger(__name__)

class EmailAlertManager:
    """Email alert manager for autonomous cryptocurrency alerts."""
    
    def __init__(self):
        self.tyler_email = config.tyler_email
        self.from_email = config.alert_from_email
        self.gmail_service = None
        self._initialize_gmail_service()
        logger.info("EmailAlertManager initialized")
    
    def _initialize_gmail_service(self):
        """Initialize Gmail API service with Secret Manager credentials."""
        try:
            # Get Gmail credentials from Secret Manager
            client = secretmanager.SecretManagerServiceClient()
            project_id = config.project_id

            # Try to get Gmail credentials
            try:
                secret_name = f"projects/{project_id}/secrets/AEGIS_TRADING_GMAIL_CREDENTIALS/versions/latest"
                response = client.access_secret_version(request={"name": secret_name})
                credentials_json = response.payload.data.decode("UTF-8")
                credentials_info = json.loads(credentials_json)

                # Create credentials with Gmail scope and domain-wide delegation
                credentials = Credentials.from_service_account_info(
                    credentials_info,
                    scopes=['https://www.googleapis.com/auth/gmail.send']
                )

                # Enable domain-wide delegation to send emails as Tyler
                credentials = credentials.with_subject(config.tyler_email)

                # Build Gmail service
                self.gmail_service = build('gmail', 'v1', credentials=credentials)
                logger.info("Gmail service initialized successfully")

            except Exception as cred_error:
                logger.warning(f"Gmail credentials not found in Secret Manager: {str(cred_error)}")
                logger.info("Gmail service will be initialized without credentials (test mode)")
                self.gmail_service = None

        except Exception as e:
            logger.error(f"Failed to initialize Gmail service: {str(e)}")
            self.gmail_service = None
    
    def _create_signal_html(self, signal: TradingSignal) -> str:
        """Create HTML representation of a trading signal."""
        confidence_color = {
            ConfidenceLevel.VERY_HIGH: "#28a745",  # Green
            ConfidenceLevel.HIGH: "#17a2b8",       # Blue
            ConfidenceLevel.MEDIUM: "#ffc107",     # Yellow
            ConfidenceLevel.LOW: "#6c757d"         # Gray
        }
        
        signal_color = {
            SignalType.BUY: "#28a745",    # Green
            SignalType.SELL: "#dc3545",   # Red
            SignalType.WATCH: "#17a2b8",  # Blue
            SignalType.HOLD: "#6c757d"    # Gray
        }
        
        html = f"""
        <div style="border: 2px solid {signal_color[signal.signal_type]}; border-radius: 8px; padding: 15px; margin: 10px 0; background-color: #f8f9fa;">
            <h3 style="color: {signal_color[signal.signal_type]}; margin-top: 0;">
                {signal.symbol.upper()} - {signal.signal_type.value.upper()}
            </h3>
            <div style="display: flex; justify-content: space-between; margin-bottom: 10px;">
                <span><strong>Confidence:</strong> 
                    <span style="color: {confidence_color[signal.confidence]}; font-weight: bold;">
                        {signal.confidence.value.replace('_', ' ').title()} ({signal.confidence_score:.1%})
                    </span>
                </span>
                <span><strong>Entry Price:</strong> ${signal.entry_price:.4f}</span>
            </div>
        """
        
        if signal.target_price:
            html += f'<div><strong>Target Price:</strong> ${signal.target_price:.4f} ({((signal.target_price/signal.entry_price-1)*100):+.1f}%)</div>'
        
        if signal.stop_loss:
            html += f'<div><strong>Stop Loss:</strong> ${signal.stop_loss:.4f} ({((signal.stop_loss/signal.entry_price-1)*100):+.1f}%)</div>'
        
        html += f"""
            <div style="margin-top: 10px; padding: 10px; background-color: #e9ecef; border-radius: 4px;">
                <strong>Analysis:</strong> {signal.reasoning}
            </div>
            <div style="margin-top: 5px; font-size: 0.9em; color: #6c757d;">
                Generated: {signal.timestamp.strftime('%Y-%m-%d %H:%M:%S UTC')}
            </div>
        </div>
        """
        
        return html
    
    def _create_market_overview_html(self, report: MarketIntelligenceReport) -> str:
        """Create HTML representation of market overview."""
        overview = report.market_overview
        sentiment = report.news_sentiment
        
        sentiment_color = {
            "positive": "#28a745",
            "negative": "#dc3545", 
            "neutral": "#6c757d"
        }
        
        html = f"""
        <div style="background-color: #e3f2fd; border-radius: 8px; padding: 20px; margin: 20px 0;">
            <h2 style="color: #1976d2; margin-top: 0;">Market Overview</h2>
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px;">
                <div>
                    <strong>Cryptocurrencies Analyzed:</strong><br>
                    <span style="font-size: 1.5em; color: #1976d2;">{overview.get('total_cryptocurrencies_analyzed', 0)}</span>
                </div>
                <div>
                    <strong>Signals Generated:</strong><br>
                    <span style="font-size: 1.5em; color: #1976d2;">{overview.get('signals_generated', 0)}</span>
                </div>
                <div>
                    <strong>High Confidence Signals:</strong><br>
                    <span style="font-size: 1.5em; color: #28a745;">{overview.get('high_confidence_signals', 0)}</span>
                </div>
                <div>
                    <strong>Market Sentiment:</strong><br>
                    <span style="font-size: 1.2em; color: {sentiment_color.get(sentiment.get('overall_sentiment', 'neutral'), '#6c757d')}; font-weight: bold;">
                        {sentiment.get('overall_sentiment', 'neutral').title()}
                    </span>
                </div>
            </div>
            <div style="margin-top: 15px;">
                <strong>News Analysis:</strong> {sentiment.get('news_count', 0)} articles analyzed 
                ({sentiment.get('positive_news', 0)} positive, {sentiment.get('negative_news', 0)} negative)
            </div>
        </div>
        """
        
        return html
    
    def _create_key_findings_html(self, findings: List[str]) -> str:
        """Create HTML representation of key findings."""
        if not findings:
            return "<p><em>No significant findings in this analysis cycle.</em></p>"
        
        html = "<h2 style='color: #1976d2;'>Key Findings</h2><ul>"
        for finding in findings:
            html += f"<li style='margin-bottom: 8px;'>{finding}</li>"
        html += "</ul>"
        
        return html
    
    def create_alert_email(self, report: MarketIntelligenceReport) -> str:
        """Create HTML email content for market intelligence report."""
        # Filter signals by confidence for email
        high_confidence_signals = [s for s in report.signals if s.confidence_score >= 0.7]
        medium_confidence_signals = [s for s in report.signals if 0.6 <= s.confidence_score < 0.7]
        
        html_content = f"""
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="UTF-8">
            <title>Aegis Trading Agent - Market Analysis</title>
        </head>
        <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333; max-width: 800px; margin: 0 auto; padding: 20px;">
            <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 20px; border-radius: 8px; text-align: center; margin-bottom: 20px;">
                <h1 style="margin: 0; font-size: 2em;">🤖 Aegis Trading Agent v3</h1>
                <p style="margin: 10px 0 0 0; font-size: 1.1em;">Autonomous Cryptocurrency Research & Alert System</p>
                <p style="margin: 5px 0 0 0; opacity: 0.9;">{report.title}</p>
            </div>
            
            <div style="background-color: #f8f9fa; border-left: 4px solid #007bff; padding: 15px; margin-bottom: 20px;">
                <h2 style="color: #007bff; margin-top: 0;">Executive Summary</h2>
                <p>{report.summary}</p>
            </div>
            
            {self._create_market_overview_html(report)}
            
            {self._create_key_findings_html(report.key_findings)}
        """
        
        # Add high confidence signals
        if high_confidence_signals:
            html_content += "<h2 style='color: #28a745;'>🎯 High Confidence Signals</h2>"
            for signal in high_confidence_signals:
                html_content += self._create_signal_html(signal)
        
        # Add medium confidence signals
        if medium_confidence_signals:
            html_content += "<h2 style='color: #ffc107;'>⚠️ Medium Confidence Signals</h2>"
            for signal in medium_confidence_signals:
                html_content += self._create_signal_html(signal)
        
        # Add footer
        html_content += f"""
            <div style="margin-top: 30px; padding: 20px; background-color: #e9ecef; border-radius: 8px; text-align: center;">
                <p style="margin: 0; color: #6c757d;">
                    <strong>Aegis Trading Agent v3</strong><br>
                    Autonomous Analysis Completed: {datetime.utcnow().strftime('%Y-%m-%d %H:%M:%S UTC')}<br>
                    Next Analysis: ~{config.analysis_interval_hours} hours<br>
                    <em>Tyler-Only Access • Complete Isolation from Business Agents</em>
                </p>
            </div>
        </body>
        </html>
        """
        
        return html_content
    
    def send_market_alert(self, report: MarketIntelligenceReport) -> bool:
        """Send market intelligence alert email to Tyler."""
        try:
            # Create email content
            html_content = self.create_alert_email(report)
            
            # Create email message
            message = MIMEMultipart('alternative')
            message['Subject'] = f"🤖 Aegis Alert: {len(report.signals)} Signals | {report.news_sentiment.get('overall_sentiment', 'neutral').title()} Sentiment"
            message['From'] = self.from_email
            message['To'] = self.tyler_email
            
            # Add HTML content
            html_part = MIMEText(html_content, 'html')
            message.attach(html_part)
            
            # Send via Gmail API
            logger.info(f"Market alert email prepared for Tyler: {len(report.signals)} signals")

            # Actually send the email via Gmail API
            success = self._send_gmail_message(message)

            if success:
                logger.info("Market intelligence email sent to Tyler")
                logger.info("Email content created successfully",
                           signals_count=len(report.signals),
                           high_confidence_count=len([s for s in report.signals if s.confidence_score >= 0.7]))
                return True
            else:
                logger.error("Failed to send market intelligence email")
                return False
            
        except Exception as e:
            logger.error(f"Failed to send market alert email: {str(e)}")
            return False
    
    def send_urgent_alert(self, signal: TradingSignal) -> bool:
        """Send urgent alert for very high confidence signals."""
        if signal.confidence != ConfidenceLevel.VERY_HIGH:
            return False
        
        try:
            subject = f"🚨 URGENT: {signal.symbol.upper()} {signal.signal_type.value.upper()} Signal"
            
            html_content = f"""
            <!DOCTYPE html>
            <html>
            <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333;">
                <div style="background-color: #dc3545; color: white; padding: 20px; text-align: center;">
                    <h1>🚨 URGENT TRADING SIGNAL</h1>
                    <p>Very High Confidence Alert from Aegis Trading Agent</p>
                </div>
                
                <div style="padding: 20px;">
                    {self._create_signal_html(signal)}
                    
                    <div style="background-color: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; border-radius: 4px; margin-top: 20px;">
                        <strong>⚡ Action Required:</strong> This is a very high confidence signal that requires immediate attention.
                    </div>
                </div>
            </body>
            </html>
            """
            
            # Create and send urgent email
            message = MIMEMultipart('alternative')
            message['Subject'] = subject
            message['From'] = self.from_email
            message['To'] = self.tyler_email
            
            html_part = MIMEText(html_content, 'html')
            message.attach(html_part)

            # Actually send the urgent email via Gmail API
            success = self._send_gmail_message(message)

            if success:
                logger.info(f"Urgent alert sent for {signal.symbol} {signal.signal_type.value} signal")
                return True
            else:
                logger.error(f"Failed to send urgent alert for {signal.symbol}")
                return False
            
        except Exception as e:
            logger.error(f"Failed to send urgent alert: {str(e)}")
            return False
    
    def test_email_system(self) -> Dict[str, Any]:
        """Test the email alert system."""
        try:
            # Create a test report
            from .analysis import TradingSignal, SignalType, ConfidenceLevel
            
            test_signal = TradingSignal(
                symbol="BTC",
                signal_type=SignalType.BUY,
                confidence=ConfidenceLevel.HIGH,
                confidence_score=0.85,
                entry_price=45000.0,
                target_price=51750.0,
                stop_loss=41400.0,
                reasoning="Test signal for email system validation"
            )
            
            test_report = MarketIntelligenceReport(
                title="Test Market Analysis",
                summary="This is a test of the Aegis email alert system.",
                key_findings=["Email system operational", "Test signal generated successfully"],
                signals=[test_signal],
                whale_activity=[],
                news_sentiment={"overall_sentiment": "neutral", "sentiment_score": 0.0},
                market_overview={"total_cryptocurrencies_analyzed": 1, "signals_generated": 1}
            )
            
            # Test email creation
            html_content = self.create_alert_email(test_report)
            
            return {
                "status": "success",
                "email_created": True,
                "html_length": len(html_content),
                "test_signal_included": "BTC" in html_content,
                "timestamp": datetime.utcnow().isoformat()
            }
            
        except Exception as e:
            logger.error(f"Email system test failed: {str(e)}")
            return {
                "status": "error",
                "error": str(e),
                "timestamp": datetime.utcnow().isoformat()
            }

    def _send_gmail_message(self, message: MIMEMultipart) -> bool:
        """Send email using Gmail API."""
        try:
            if not self.gmail_service:
                logger.warning("Gmail service not initialized - email not sent")
                return False

            # Convert message to raw format
            raw_message = base64.urlsafe_b64encode(message.as_bytes()).decode('utf-8')

            # Send the message
            send_message = {'raw': raw_message}
            result = self.gmail_service.users().messages().send(
                userId='me',
                body=send_message
            ).execute()

            logger.info(f"Email sent successfully. Message ID: {result.get('id')}")
            return True

        except Exception as e:
            logger.error(f"Failed to send email via Gmail API: {str(e)}")
            return False

    async def send_test_email(self) -> Dict[str, Any]:
        """Send a test email to Tyler to validate Gmail API integration."""
        try:
            subject = "🤖 Aegis Trading Agent v3 - Test Email"

            html_content = f"""
            <!DOCTYPE html>
            <html>
            <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333;">
                <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 20px; text-align: center;">
                    <h1>🤖 Aegis Trading Agent v3</h1>
                    <p>Gmail API Integration Test</p>
                </div>

                <div style="padding: 20px;">
                    <h2>✅ Email System Test</h2>
                    <p>This is a test email to validate the Gmail API integration for autonomous cryptocurrency alerts.</p>

                    <div style="background-color: #e7f3ff; border-left: 4px solid #2196F3; padding: 15px; margin: 20px 0;">
                        <h3>📧 Email Configuration</h3>
                        <ul>
                            <li><strong>From:</strong> <EMAIL></li>
                            <li><strong>To:</strong> <EMAIL></li>
                            <li><strong>Authentication:</strong> Service Account with Domain-Wide Delegation</li>
                            <li><strong>Timestamp:</strong> {datetime.utcnow().strftime('%Y-%m-%d %H:%M:%S')} UTC</li>
                        </ul>
                    </div>

                    <div style="background-color: #f0f9ff; border: 1px solid #0ea5e9; padding: 15px; border-radius: 4px; margin: 20px 0;">
                        <h3>🚀 Next Steps</h3>
                        <p>If you receive this email, the Gmail API integration is working correctly and ready for:</p>
                        <ul>
                            <li>📊 Regular market intelligence reports (every 4 hours)</li>
                            <li>🚨 Urgent high-confidence trading signals</li>
                            <li>📈 Autonomous cryptocurrency research alerts</li>
                        </ul>
                    </div>

                    <div style="background-color: #f8f9fa; padding: 15px; border-radius: 4px; margin-top: 20px; text-align: center;">
                        <p><strong>Aegis Trading Agent v3 - Phase 4: Milestone 4.2 Complete</strong></p>
                        <p style="color: #6c757d; font-size: 0.9em;">Autonomous Cryptocurrency Research & Alert System</p>
                    </div>
                </div>
            </body>
            </html>
            """

            # Create email message
            message = MIMEMultipart('alternative')
            message['Subject'] = subject
            message['From'] = self.from_email
            message['To'] = self.tyler_email

            html_part = MIMEText(html_content, 'html')
            message.attach(html_part)

            # Send the email
            success = self._send_gmail_message(message)

            return {
                "status": "success" if success else "failed",
                "email_sent": success,
                "gmail_service_available": self.gmail_service is not None,
                "from_email": self.from_email,
                "to_email": self.tyler_email,
                "timestamp": datetime.utcnow().isoformat()
            }

        except Exception as e:
            logger.error(f"Failed to send test email: {str(e)}")
            return {
                "status": "error",
                "error": str(e),
                "timestamp": datetime.utcnow().isoformat()
            }
