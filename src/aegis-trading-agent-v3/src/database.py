# Aegis Trading Agent v3 - Enhanced Intelligence System Database Module
# PostgreSQL with TimescaleDB for time-series cryptocurrency data
# Tyler-Only Access - Complete Isolation from Business Systems

import asyncio
import asyncpg
from typing import Dict, List, Any, Optional, Union
from datetime import datetime, timedelta
from dataclasses import dataclass, asdict
from decimal import Decimal
import json
import structlog
from contextlib import asynccontextmanager

from .config import config

logger = structlog.get_logger(__name__)

# =====================================================
# DATA MODELS
# =====================================================

@dataclass
class MarketDataRecord:
    """Market data record for database storage."""
    symbol: str
    name: Optional[str]
    price: Decimal
    volume_24h: Optional[int]
    market_cap: Optional[int]
    price_change_24h: Optional[Decimal]
    price_change_7d: Optional[Decimal]
    market_cap_rank: Optional[int]
    circulating_supply: Optional[int]
    total_supply: Optional[int]
    max_supply: Optional[int]
    ath: Optional[Decimal]
    ath_change_percentage: Optional[Decimal]
    ath_date: Optional[datetime]
    atl: Optional[Decimal]
    atl_change_percentage: Optional[Decimal]
    atl_date: Optional[datetime]
    roi: Optional[Dict[str, Any]]
    last_updated: datetime
    data_source: str
    timestamp: Optional[datetime] = None

@dataclass
class MacroScanResult:
    """Macro scan result for database storage."""
    scan_type: str
    total_cryptos_analyzed: int
    market_summary: Dict[str, Any]
    sector_analysis: Optional[Dict[str, Any]]
    top_gainers: Optional[Dict[str, Any]]
    top_losers: Optional[Dict[str, Any]]
    volume_spikes: Optional[Dict[str, Any]]
    opportunity_score: Optional[Decimal]
    opportunities_detected: int
    triggers_activated: Optional[Dict[str, Any]]
    execution_time_seconds: Optional[Decimal]
    timestamp: Optional[datetime] = None

@dataclass
class AnalysisResult:
    """Analysis result for database storage."""
    analysis_type: str
    symbols: List[str]
    analysis_trigger: Optional[str]
    technical_analysis: Dict[str, Any]
    ai_insights: Dict[str, Any]
    market_context: Optional[Dict[str, Any]]
    confidence_scores: Dict[str, Any]
    risk_assessment: Optional[Dict[str, Any]]
    execution_time_seconds: Optional[Decimal]
    timestamp: Optional[datetime] = None

@dataclass
class PredictionRecord:
    """Prediction record for database storage."""
    symbol: str
    prediction_type: str
    signal_strength: str
    confidence_score: Decimal
    current_price: Decimal
    target_price: Optional[Decimal]
    stop_loss_price: Optional[Decimal]
    time_horizon_hours: Optional[int]
    reasoning: str
    technical_indicators: Optional[Dict[str, Any]]
    market_conditions: Optional[Dict[str, Any]]
    analysis_id: Optional[int]
    actual_outcome: Optional[str] = None
    outcome_price: Optional[Decimal] = None
    outcome_timestamp: Optional[datetime] = None
    accuracy_score: Optional[Decimal] = None
    profit_loss_percentage: Optional[Decimal] = None
    created_timestamp: Optional[datetime] = None
    updated_timestamp: Optional[datetime] = None

# =====================================================
# DATABASE CONNECTION MANAGER
# =====================================================

class DatabaseManager:
    """Async PostgreSQL database manager for Aegis Trading Agent v3."""
    
    def __init__(self):
        self.connection_pool: Optional[asyncpg.Pool] = None
        self.db_credentials = None

    async def _get_database_credentials(self) -> Dict[str, str]:
        """Get database credentials from Secret Manager."""
        if self.db_credentials:
            return self.db_credentials

        try:
            from google.cloud import secretmanager
            client = secretmanager.SecretManagerServiceClient()
            project_id = "vertex-ai-agent-yzdlnjey"

            # Retrieve database credentials
            host_secret = client.access_secret_version(
                request={"name": f"projects/{project_id}/secrets/AEGIS_TRADING_DATABASE_HOST/versions/latest"}
            )
            db_host = host_secret.payload.data.decode("UTF-8").strip()

            name_secret = client.access_secret_version(
                request={"name": f"projects/{project_id}/secrets/AEGIS_TRADING_DATABASE_NAME/versions/latest"}
            )
            db_name = name_secret.payload.data.decode("UTF-8").strip()

            user_secret = client.access_secret_version(
                request={"name": f"projects/{project_id}/secrets/AEGIS_TRADING_DATABASE_USER/versions/latest"}
            )
            db_user = user_secret.payload.data.decode("UTF-8").strip()

            password_secret = client.access_secret_version(
                request={"name": f"projects/{project_id}/secrets/AEGIS_TRADING_DATABASE_PASSWORD/versions/latest"}
            )
            db_password = password_secret.payload.data.decode("UTF-8").strip()

            self.db_credentials = {
                "host": db_host,
                "database": db_name,
                "user": db_user,
                "password": db_password
            }

            return self.db_credentials

        except Exception as e:
            logger.error(f"Failed to get database credentials: {str(e)}")
            raise
    
    async def initialize(self) -> None:
        """Initialize database connection pool."""
        try:
            # Get database credentials
            credentials = await self._get_database_credentials()

            # Create connection pool
            if credentials["host"].startswith("/cloudsql/"):
                # Unix socket connection for Cloud SQL
                self.connection_pool = await asyncpg.create_pool(
                    host=credentials["host"],
                    database=credentials["database"],
                    user=credentials["user"],
                    password=credentials["password"],
                    min_size=2,
                    max_size=10,
                    command_timeout=60,
                    server_settings={
                        'jit': 'off'  # Disable JIT for better performance with small queries
                    }
                )
            else:
                # TCP connection
                self.connection_pool = await asyncpg.create_pool(
                    host=credentials["host"],
                    port=5432,
                    database=credentials["database"],
                    user=credentials["user"],
                    password=credentials["password"],
                    ssl="require",
                    min_size=2,
                    max_size=10,
                    command_timeout=60,
                    server_settings={
                        'jit': 'off'  # Disable JIT for better performance with small queries
                    }
                )

            logger.info("Database connection pool initialized")

            # Test connection
            async with self.connection_pool.acquire() as conn:
                result = await conn.fetchval("SELECT version()")
                logger.info(f"Connected to database: {result}")

        except Exception as e:
            logger.error(f"Failed to initialize database connection: {str(e)}")
            raise
    
    async def close(self) -> None:
        """Close database connection pool."""
        if self.connection_pool:
            await self.connection_pool.close()
            logger.info("Database connection pool closed")
    
    @asynccontextmanager
    async def get_connection(self):
        """Get database connection from pool."""
        if not self.connection_pool:
            await self.initialize()
        
        async with self.connection_pool.acquire() as connection:
            yield connection
    
    # =====================================================
    # MARKET DATA OPERATIONS
    # =====================================================
    
    async def store_market_data(self, market_data: List[MarketDataRecord]) -> int:
        """Store market data records in database."""
        if not market_data:
            return 0
        
        try:
            async with self.get_connection() as conn:
                # Prepare data for insertion
                records = []
                for data in market_data:
                    record = (
                        data.symbol, data.name, data.price, data.volume_24h,
                        data.market_cap, data.price_change_24h, data.price_change_7d,
                        data.market_cap_rank, data.circulating_supply, data.total_supply,
                        data.max_supply, data.ath, data.ath_change_percentage, data.ath_date,
                        data.atl, data.atl_change_percentage, data.atl_date,
                        json.dumps(data.roi) if data.roi else None,
                        data.last_updated, data.data_source,
                        data.timestamp or datetime.utcnow()
                    )
                    records.append(record)
                
                # Bulk insert
                await conn.executemany("""
                    INSERT INTO market_data_history (
                        symbol, name, price, volume_24h, market_cap, price_change_24h,
                        price_change_7d, market_cap_rank, circulating_supply, total_supply,
                        max_supply, ath, ath_change_percentage, ath_date, atl,
                        atl_change_percentage, atl_date, roi, last_updated, data_source, timestamp
                    ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16, $17, $18, $19, $20, $21)
                """, records)
                
                logger.info(f"Stored {len(records)} market data records")
                return len(records)
                
        except Exception as e:
            logger.error(f"Error storing market data: {str(e)}")
            raise
    
    async def get_latest_market_data(self, symbols: Optional[List[str]] = None) -> List[Dict[str, Any]]:
        """Get latest market data for specified symbols or all symbols."""
        try:
            async with self.get_connection() as conn:
                if symbols:
                    query = """
                        SELECT DISTINCT ON (symbol) 
                            symbol, name, price, volume_24h, market_cap, price_change_24h,
                            market_cap_rank, timestamp
                        FROM market_data_history 
                        WHERE symbol = ANY($1)
                        ORDER BY symbol, timestamp DESC
                    """
                    rows = await conn.fetch(query, symbols)
                else:
                    query = """
                        SELECT DISTINCT ON (symbol) 
                            symbol, name, price, volume_24h, market_cap, price_change_24h,
                            market_cap_rank, timestamp
                        FROM market_data_history 
                        ORDER BY symbol, timestamp DESC
                    """
                    rows = await conn.fetch(query)
                
                return [dict(row) for row in rows]
                
        except Exception as e:
            logger.error(f"Error getting latest market data: {str(e)}")
            return []
    
    async def get_historical_data(self, symbol: str, hours: int = 24) -> List[Dict[str, Any]]:
        """Get historical data for a symbol within specified time range."""
        try:
            async with self.get_connection() as conn:
                query = """
                    SELECT symbol, price, volume_24h, market_cap, price_change_24h, timestamp
                    FROM market_data_history 
                    WHERE symbol = $1 AND timestamp >= $2
                    ORDER BY timestamp DESC
                """
                since = datetime.utcnow() - timedelta(hours=hours)
                rows = await conn.fetch(query, symbol, since)
                
                return [dict(row) for row in rows]
                
        except Exception as e:
            logger.error(f"Error getting historical data for {symbol}: {str(e)}")
            return []
    
    # =====================================================
    # MACRO SCAN OPERATIONS
    # =====================================================
    
    async def store_macro_scan_result(self, scan_result: MacroScanResult) -> int:
        """Store macro scan result in database."""
        try:
            async with self.get_connection() as conn:
                query = """
                    INSERT INTO macro_scan_results (
                        scan_type, total_cryptos_analyzed, market_summary, sector_analysis,
                        top_gainers, top_losers, volume_spikes, opportunity_score,
                        opportunities_detected, triggers_activated, execution_time_seconds, timestamp
                    ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12)
                    RETURNING id
                """
                
                result_id = await conn.fetchval(
                    query,
                    scan_result.scan_type,
                    scan_result.total_cryptos_analyzed,
                    json.dumps(scan_result.market_summary),
                    json.dumps(scan_result.sector_analysis) if scan_result.sector_analysis else None,
                    json.dumps(scan_result.top_gainers) if scan_result.top_gainers else None,
                    json.dumps(scan_result.top_losers) if scan_result.top_losers else None,
                    json.dumps(scan_result.volume_spikes) if scan_result.volume_spikes else None,
                    scan_result.opportunity_score,
                    scan_result.opportunities_detected,
                    json.dumps(scan_result.triggers_activated) if scan_result.triggers_activated else None,
                    scan_result.execution_time_seconds,
                    scan_result.timestamp or datetime.utcnow()
                )
                
                logger.info(f"Stored macro scan result with ID: {result_id}")
                return result_id
                
        except Exception as e:
            logger.error(f"Error storing macro scan result: {str(e)}")
            raise
    
    # =====================================================
    # PREDICTION OPERATIONS
    # =====================================================
    
    async def store_prediction(self, prediction: PredictionRecord) -> int:
        """Store prediction in database."""
        try:
            async with self.get_connection() as conn:
                query = """
                    INSERT INTO predictions (
                        symbol, prediction_type, signal_strength, confidence_score,
                        current_price, target_price, stop_loss_price, time_horizon_hours,
                        reasoning, technical_indicators, market_conditions, analysis_id,
                        created_timestamp, updated_timestamp
                    ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14)
                    RETURNING id
                """
                
                prediction_id = await conn.fetchval(
                    query,
                    prediction.symbol,
                    prediction.prediction_type,
                    prediction.signal_strength,
                    prediction.confidence_score,
                    prediction.current_price,
                    prediction.target_price,
                    prediction.stop_loss_price,
                    prediction.time_horizon_hours,
                    prediction.reasoning,
                    json.dumps(prediction.technical_indicators) if prediction.technical_indicators else None,
                    json.dumps(prediction.market_conditions) if prediction.market_conditions else None,
                    prediction.analysis_id,
                    prediction.created_timestamp or datetime.utcnow(),
                    prediction.updated_timestamp or datetime.utcnow()
                )
                
                logger.info(f"Stored prediction for {prediction.symbol} with ID: {prediction_id}")
                return prediction_id
                
        except Exception as e:
            logger.error(f"Error storing prediction: {str(e)}")
            raise
    
    async def get_recent_predictions(self, hours: int = 24, min_confidence: float = 0.7) -> List[Dict[str, Any]]:
        """Get recent high-confidence predictions."""
        try:
            async with self.get_connection() as conn:
                query = """
                    SELECT symbol, prediction_type, confidence_score, current_price,
                           target_price, reasoning, created_timestamp
                    FROM predictions 
                    WHERE created_timestamp >= $1 AND confidence_score >= $2
                    ORDER BY created_timestamp DESC
                """
                since = datetime.utcnow() - timedelta(hours=hours)
                rows = await conn.fetch(query, since, min_confidence)
                
                return [dict(row) for row in rows]
                
        except Exception as e:
            logger.error(f"Error getting recent predictions: {str(e)}")
            return []

    async def get_market_data_statistics(self) -> Dict[str, Any]:
        """Get market data statistics for monitoring."""
        try:
            async with self.get_connection() as conn:
                # Get total records count
                total_records = await conn.fetchval("SELECT COUNT(*) FROM market_data_history")

                # Get unique symbols count
                unique_symbols = await conn.fetchval("SELECT COUNT(DISTINCT symbol) FROM market_data_history")

                # Get latest data timestamp
                latest_timestamp = await conn.fetchval("SELECT MAX(timestamp) FROM market_data_history")

                # Get oldest data timestamp
                oldest_timestamp = await conn.fetchval("SELECT MIN(timestamp) FROM market_data_history")

                # Get data sources
                data_sources = await conn.fetch("SELECT data_source, COUNT(*) as count FROM market_data_history GROUP BY data_source")

                return {
                    "total_records": total_records or 0,
                    "unique_symbols": unique_symbols or 0,
                    "latest_timestamp": latest_timestamp.isoformat() if latest_timestamp else None,
                    "oldest_timestamp": oldest_timestamp.isoformat() if oldest_timestamp else None,
                    "data_sources": {row['data_source']: row['count'] for row in data_sources}
                }

        except Exception as e:
            logger.error(f"Error getting market data statistics: {str(e)}")
            return {
                "total_records": 0,
                "unique_symbols": 0,
                "latest_timestamp": None,
                "oldest_timestamp": None,
                "data_sources": {}
            }

# =====================================================
# GLOBAL DATABASE INSTANCE
# =====================================================

# Global database manager instance
db_manager = DatabaseManager()

# Convenience functions for common operations
async def store_market_data(market_data: List[MarketDataRecord]) -> int:
    """Store market data records."""
    return await db_manager.store_market_data(market_data)

async def get_latest_market_data(symbols: Optional[List[str]] = None) -> List[Dict[str, Any]]:
    """Get latest market data."""
    return await db_manager.get_latest_market_data(symbols)

async def store_macro_scan_result(scan_result: MacroScanResult) -> int:
    """Store macro scan result."""
    return await db_manager.store_macro_scan_result(scan_result)

async def store_prediction(prediction: PredictionRecord) -> int:
    """Store prediction."""
    return await db_manager.store_prediction(prediction)
