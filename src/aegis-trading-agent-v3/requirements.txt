# Aegis Trading Agent v3 - Minimal Requirements for Phase 1 Testing
# Following proven agent template methodology

# Core FastAPI dependencies
fastapi>=0.110.0
uvicorn[standard]>=0.29.0
gunicorn>=21.2.0
pydantic>=2.7.0
pydantic-settings>=2.2.0

# Google Cloud Platform dependencies
google-cloud-secret-manager>=2.18.0
google-cloud-logging>=3.8.0
google-auth>=2.23.0

# Database dependencies
asyncpg>=0.29.0

# LangChain and AI dependencies
langchain>=0.1.0
langchain-core>=0.1.0
langchain-google-vertexai>=0.1.0
langgraph>=0.0.20

# Gmail API
google-api-python-client>=2.108.0
google-auth-oauthlib>=1.1.0
google-auth-httplib2>=0.1.1

# HTTP and API dependencies
aiohttp>=3.9.0
httpx>=0.25.0

# Essential utilities
structlog>=23.2.0
python-dateutil>=2.8.0
requests>=2.31.0
python-dotenv>=1.0.0

# Data processing (essential for analysis)
numpy>=1.24.0
pandas>=2.0.0

# Crypto API
ccxt>=4.1.0


