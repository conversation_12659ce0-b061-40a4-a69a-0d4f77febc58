-- Aegis Trading Agent v3 - Enhanced Intelligence System Database Schema
-- PostgreSQL for time-series cryptocurrency data
-- Tyler-Only Access - Complete Isolation from Business Systems

-- Note: TimescaleDB not available on Cloud SQL, using standard PostgreSQL with time-series optimizations

-- =====================================================
-- 1. MARKET DATA HISTORY (Time-Series Optimized)
-- =====================================================

CREATE TABLE market_data_history (
    id BIGSERIAL PRIMARY KEY,
    symbol VARCHAR(50) NOT NULL,
    name VARCHAR(100),
    price DECIMAL(20,8) NOT NULL,
    volume_24h BIGINT,
    market_cap BIGINT,
    price_change_24h DECIMAL(10,4),
    price_change_7d DECIMAL(10,4),
    market_cap_rank INTEGER,
    circulating_supply BIGINT,
    total_supply BIGINT,
    max_supply BIGINT,
    ath DECIMAL(20,8),
    ath_change_percentage DECIMAL(10,4),
    ath_date TIMESTAMPTZ,
    atl DECIMAL(20,8),
    atl_change_percentage DECIMAL(10,4),
    atl_date TIMESTAMPTZ,
    roi JSONB, -- Return on investment data
    last_updated TIMESTAMPTZ NOT NULL,
    data_source VARCHAR(50) NOT NULL, -- 'coingecko', 'dex_screener', etc.
    timestamp TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- Time-series optimization with standard PostgreSQL indexing

-- Create indexes for efficient querying
CREATE INDEX idx_market_data_symbol_time ON market_data_history (symbol, timestamp DESC);
CREATE INDEX idx_market_data_source_time ON market_data_history (data_source, timestamp DESC);
CREATE INDEX idx_market_data_rank_time ON market_data_history (market_cap_rank, timestamp DESC);

-- =====================================================
-- 2. MACRO SCAN RESULTS (Hourly Market Analysis)
-- =====================================================

CREATE TABLE macro_scan_results (
    id BIGSERIAL PRIMARY KEY,
    scan_type VARCHAR(50) NOT NULL, -- 'hourly_macro', 'sector_analysis', 'opportunity_scan'
    total_cryptos_analyzed INTEGER NOT NULL,
    market_summary JSONB NOT NULL, -- Overall market metrics
    sector_analysis JSONB, -- Sector performance breakdown
    top_gainers JSONB, -- Top performing cryptocurrencies
    top_losers JSONB, -- Worst performing cryptocurrencies
    volume_spikes JSONB, -- Unusual volume activity
    opportunity_score DECIMAL(4,3), -- Overall market opportunity score (0-1)
    opportunities_detected INTEGER DEFAULT 0,
    triggers_activated JSONB, -- Which deep-dive triggers were activated
    execution_time_seconds DECIMAL(8,3),
    timestamp TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- Time-series optimization with standard PostgreSQL indexing

-- Create indexes
CREATE INDEX idx_macro_scan_type_time ON macro_scan_results (scan_type, timestamp DESC);
CREATE INDEX idx_macro_scan_opportunity ON macro_scan_results (opportunity_score DESC, timestamp DESC);

-- =====================================================
-- 3. ANALYSIS RESULTS (Deep-Dive Analysis Storage)
-- =====================================================

CREATE TABLE analysis_results (
    id BIGSERIAL PRIMARY KEY,
    analysis_type VARCHAR(50) NOT NULL, -- 'deep_analysis', 'sector_analysis', 'parallel_analysis'
    symbols TEXT[] NOT NULL, -- Array of symbols analyzed
    analysis_trigger VARCHAR(100), -- What triggered this analysis
    technical_analysis JSONB NOT NULL, -- Technical indicators results
    ai_insights JSONB NOT NULL, -- AI-generated insights and reasoning
    market_context JSONB, -- Market conditions during analysis
    confidence_scores JSONB NOT NULL, -- Confidence scores for various predictions
    risk_assessment JSONB, -- Risk analysis results
    execution_time_seconds DECIMAL(8,3),
    timestamp TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- Time-series optimization with standard PostgreSQL indexing

-- Create indexes
CREATE INDEX idx_analysis_type_time ON analysis_results (analysis_type, timestamp DESC);
CREATE INDEX idx_analysis_symbols ON analysis_results USING GIN (symbols);

-- =====================================================
-- 4. PREDICTIONS & SIGNALS (Performance Tracking)
-- =====================================================

CREATE TABLE predictions (
    id BIGSERIAL PRIMARY KEY,
    symbol VARCHAR(50) NOT NULL,
    prediction_type VARCHAR(50) NOT NULL, -- 'buy', 'sell', 'watch', 'hold'
    signal_strength VARCHAR(20) NOT NULL, -- 'weak', 'moderate', 'strong', 'very_strong'
    confidence_score DECIMAL(4,3) NOT NULL, -- 0.000 to 1.000
    current_price DECIMAL(20,8) NOT NULL,
    target_price DECIMAL(20,8),
    stop_loss_price DECIMAL(20,8),
    time_horizon_hours INTEGER, -- Expected time for prediction to materialize
    reasoning TEXT NOT NULL, -- AI reasoning for the prediction
    technical_indicators JSONB, -- Supporting technical analysis
    market_conditions JSONB, -- Market context at time of prediction
    
    -- Outcome tracking (filled later)
    actual_outcome VARCHAR(50), -- 'success', 'failure', 'partial', 'pending'
    outcome_price DECIMAL(20,8), -- Actual price when outcome determined
    outcome_timestamp TIMESTAMPTZ, -- When outcome was determined
    accuracy_score DECIMAL(4,3), -- Calculated accuracy (0-1)
    profit_loss_percentage DECIMAL(10,4), -- Theoretical P&L percentage
    
    -- Metadata
    analysis_id BIGINT REFERENCES analysis_results(id),
    created_timestamp TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_timestamp TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- Time-series optimization with standard PostgreSQL indexing

-- Create indexes
CREATE INDEX idx_predictions_symbol_time ON predictions (symbol, created_timestamp DESC);
CREATE INDEX idx_predictions_type_confidence ON predictions (prediction_type, confidence_score DESC);
CREATE INDEX idx_predictions_outcome ON predictions (actual_outcome, accuracy_score DESC);

-- =====================================================
-- 5. BACKTESTING RESULTS (Performance Analytics)
-- =====================================================

CREATE TABLE backtesting_results (
    id BIGSERIAL PRIMARY KEY,
    backtest_name VARCHAR(100) NOT NULL,
    strategy_name VARCHAR(100) NOT NULL,
    test_period_start TIMESTAMPTZ NOT NULL,
    test_period_end TIMESTAMPTZ NOT NULL,
    symbols_tested TEXT[] NOT NULL,
    total_predictions INTEGER NOT NULL,
    successful_predictions INTEGER NOT NULL,
    accuracy_percentage DECIMAL(6,3) NOT NULL,
    average_confidence DECIMAL(4,3) NOT NULL,
    total_profit_loss_percentage DECIMAL(10,4),
    sharpe_ratio DECIMAL(8,4),
    max_drawdown_percentage DECIMAL(8,4),
    win_rate DECIMAL(6,3),
    average_holding_time_hours DECIMAL(8,2),
    strategy_parameters JSONB, -- Strategy configuration used
    detailed_results JSONB NOT NULL, -- Detailed prediction-by-prediction results
    performance_metrics JSONB NOT NULL, -- Additional performance metrics
    timestamp TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- Create indexes
CREATE INDEX idx_backtesting_strategy_time ON backtesting_results (strategy_name, timestamp DESC);
CREATE INDEX idx_backtesting_accuracy ON backtesting_results (accuracy_percentage DESC);

-- =====================================================
-- 6. SECTOR ANALYSIS (Cryptocurrency Categories)
-- =====================================================

CREATE TABLE sector_analysis (
    id BIGSERIAL PRIMARY KEY,
    sector_name VARCHAR(100) NOT NULL, -- 'DeFi', 'Gaming', 'AI', 'Layer1', etc.
    sector_symbols TEXT[] NOT NULL, -- Cryptocurrencies in this sector
    total_market_cap BIGINT,
    sector_performance_24h DECIMAL(10,4),
    sector_performance_7d DECIMAL(10,4),
    sector_performance_30d DECIMAL(10,4),
    volume_24h BIGINT,
    top_performers JSONB, -- Best performing coins in sector
    sector_trends JSONB, -- Identified trends and patterns
    opportunity_score DECIMAL(4,3), -- Sector opportunity score (0-1)
    ai_analysis JSONB, -- AI insights about sector
    timestamp TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- Time-series optimization with standard PostgreSQL indexing

-- Create indexes
CREATE INDEX idx_sector_name_time ON sector_analysis (sector_name, timestamp DESC);
CREATE INDEX idx_sector_opportunity ON sector_analysis (opportunity_score DESC, timestamp DESC);

-- =====================================================
-- 7. PATTERN RECOGNITION (Learning System)
-- =====================================================

CREATE TABLE pattern_recognition (
    id BIGSERIAL PRIMARY KEY,
    pattern_name VARCHAR(100) NOT NULL,
    pattern_type VARCHAR(50) NOT NULL, -- 'price_pattern', 'volume_pattern', 'sentiment_pattern'
    pattern_description TEXT NOT NULL,
    detection_algorithm VARCHAR(100) NOT NULL,
    success_rate DECIMAL(6,3), -- Historical success rate of this pattern
    confidence_threshold DECIMAL(4,3), -- Minimum confidence to trigger
    symbols_detected TEXT[], -- Symbols where pattern was detected
    pattern_data JSONB NOT NULL, -- Pattern-specific data and parameters
    historical_performance JSONB, -- Historical performance of this pattern
    last_detected TIMESTAMPTZ,
    detection_count INTEGER DEFAULT 0,
    timestamp TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- Create indexes
CREATE INDEX idx_pattern_type_success ON pattern_recognition (pattern_type, success_rate DESC);
CREATE INDEX idx_pattern_last_detected ON pattern_recognition (last_detected DESC);

-- =====================================================
-- 8. SYSTEM PERFORMANCE (Monitoring & Optimization)
-- =====================================================

CREATE TABLE system_performance (
    id BIGSERIAL PRIMARY KEY,
    operation_type VARCHAR(50) NOT NULL, -- 'macro_scan', 'deep_analysis', 'prediction', 'backtesting'
    execution_time_seconds DECIMAL(8,3) NOT NULL,
    memory_usage_mb INTEGER,
    api_calls_made INTEGER,
    data_points_processed INTEGER,
    success_status BOOLEAN NOT NULL,
    error_message TEXT,
    performance_metrics JSONB, -- Additional performance data
    timestamp TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- Time-series optimization with standard PostgreSQL indexing

-- Create indexes
CREATE INDEX idx_system_perf_operation_time ON system_performance (operation_type, timestamp DESC);
CREATE INDEX idx_system_perf_success ON system_performance (success_status, execution_time_seconds);

-- =====================================================
-- 9. DATA RETENTION (Manual cleanup procedures)
-- =====================================================

-- Note: Without TimescaleDB, retention policies must be implemented manually
-- Consider creating scheduled jobs to clean up old data:
-- DELETE FROM market_data_history WHERE timestamp < NOW() - INTERVAL '2 years';
-- DELETE FROM macro_scan_results WHERE timestamp < NOW() - INTERVAL '1 year';
-- DELETE FROM system_performance WHERE timestamp < NOW() - INTERVAL '6 months';

-- =====================================================
-- 10. VIEWS FOR COMMON QUERIES
-- =====================================================

-- Latest market data for all cryptocurrencies
CREATE VIEW latest_market_data AS
SELECT DISTINCT ON (symbol) 
    symbol, name, price, volume_24h, market_cap, price_change_24h, 
    market_cap_rank, timestamp
FROM market_data_history 
ORDER BY symbol, timestamp DESC;

-- Recent high-confidence predictions
CREATE VIEW recent_predictions AS
SELECT symbol, prediction_type, confidence_score, current_price, target_price,
       reasoning, created_timestamp
FROM predictions 
WHERE created_timestamp >= NOW() - INTERVAL '7 days'
  AND confidence_score >= 0.75
ORDER BY created_timestamp DESC;

-- Sector performance summary
CREATE VIEW sector_performance_summary AS
SELECT DISTINCT ON (sector_name)
    sector_name, sector_performance_24h, sector_performance_7d,
    opportunity_score, timestamp
FROM sector_analysis
ORDER BY sector_name, timestamp DESC;
