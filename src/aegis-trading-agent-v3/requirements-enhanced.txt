# Aegis Trading Agent v3 - Enhanced Intelligence System Requirements
# PostgreSQL Database + Macro Scanning + Predictive Analytics

# =====================================================
# CORE DEPENDENCIES (from Phase 3)
# =====================================================

# FastAPI and async web framework
fastapi==0.104.1
uvicorn[standard]==0.24.0
pydantic==2.5.0
pydantic-settings==2.1.0

# Google Cloud Platform
google-cloud-secret-manager==2.18.1
google-cloud-logging==3.8.0
google-cloud-monitoring==2.16.0
google-auth==2.23.4

# Vertex AI and LangChain
google-cloud-aiplatform==1.38.1
langchain==0.1.0
langchain-google-vertexai==0.1.0
langgraph==0.0.20
langchain-core==0.1.0

# Gmail API
google-api-python-client==2.108.0
google-auth-oauthlib==1.1.0
google-auth-httplib2==0.1.1

# HTTP and API clients
aiohttp==3.9.1
httpx==0.25.2
requests==2.31.0

# Data processing and analysis
pandas==2.1.4
numpy==1.25.2
python-dateutil==2.8.2

# Technical analysis
TA-Lib==0.4.28
talib-binary==0.4.19  # Binary wheels for easier installation

# Logging and monitoring
structlog==23.2.0
python-json-logger==2.0.7

# Environment and configuration
python-dotenv==1.0.0
python-multipart==0.0.6

# =====================================================
# ENHANCED DATABASE DEPENDENCIES
# =====================================================

# PostgreSQL async driver
asyncpg==0.29.0

# Database ORM and migrations
sqlalchemy[asyncio]==2.0.23
alembic==1.13.1

# Connection pooling and management
psycopg2-binary==2.9.9
asyncio-pool==0.6.0

# =====================================================
# ENHANCED ANALYTICS DEPENDENCIES
# =====================================================

# Scientific computing and statistics
scipy==1.11.4
scikit-learn==1.3.2
statsmodels==0.14.0

# Time series analysis
prophet==1.1.5
arch==6.2.0  # GARCH models for volatility
pykalman==0.9.5  # Kalman filters

# Machine learning for pattern recognition
xgboost==2.0.2
lightgbm==4.1.0
catboost==1.2.2

# Deep learning (optional, for advanced patterns)
torch==2.1.1
transformers==4.36.0

# =====================================================
# ENHANCED DATA PROCESSING
# =====================================================

# Advanced data manipulation
polars==0.20.2  # Fast DataFrame library
pyarrow==14.0.1  # Columnar data format

# Parallel processing
joblib==1.3.2
concurrent-futures==3.1.1
multiprocessing-logging==0.3.4

# Caching and performance
redis==5.0.1
diskcache==5.6.3
cachetools==5.3.2

# =====================================================
# ENHANCED CRYPTO DATA SOURCES
# =====================================================

# Additional crypto APIs
ccxt==4.1.64  # Cryptocurrency exchange library
python-binance==1.0.19  # Binance API
websocket-client==1.6.4  # WebSocket support
websockets==12.0

# Market data providers
yfinance==0.2.28  # Yahoo Finance (for traditional markets)
alpha-vantage==2.3.1  # Alpha Vantage API

# =====================================================
# BACKTESTING AND PERFORMANCE
# =====================================================

# Backtesting frameworks
backtrader==**********
zipline-reloaded==3.0.3
vectorbt==0.25.2

# Performance metrics
pyfolio==0.9.2
empyrical==0.5.5
quantstats==0.0.62

# =====================================================
# VISUALIZATION AND REPORTING
# =====================================================

# Plotting and charts
plotly==5.17.0
matplotlib==3.8.2
seaborn==0.13.0

# Report generation
jinja2==3.1.2
weasyprint==60.2  # PDF generation
markdown==3.5.1

# =====================================================
# MONITORING AND OBSERVABILITY
# =====================================================

# Application monitoring
prometheus-client==0.19.0
opentelemetry-api==1.21.0
opentelemetry-sdk==1.21.0

# Health checks and metrics
psutil==5.9.6  # System metrics
memory-profiler==0.61.0

# =====================================================
# DEVELOPMENT AND TESTING
# =====================================================

# Testing framework
pytest==7.4.3
pytest-asyncio==0.21.1
pytest-mock==3.12.0
pytest-cov==4.1.0

# Code quality
black==23.11.0
isort==5.12.0
flake8==6.1.0
mypy==1.7.1

# =====================================================
# SECURITY AND VALIDATION
# =====================================================

# Input validation and security
validators==0.22.0
cryptography==41.0.7
bcrypt==4.1.2

# Rate limiting
slowapi==0.1.9
limits==3.6.0

# =====================================================
# UTILITIES
# =====================================================

# Date and time handling
pytz==2023.3
arrow==1.3.0

# Configuration and settings
click==8.1.7
typer==0.9.0

# File handling
openpyxl==3.1.2  # Excel files
python-magic==0.4.27  # File type detection

# =====================================================
# OPTIONAL ADVANCED FEATURES
# =====================================================

# Natural language processing (for news sentiment)
nltk==3.8.1
textblob==0.17.1
spacy==3.7.2

# Image processing (for chart pattern recognition)
pillow==10.1.0
opencv-python==4.8.1.78

# Blockchain analysis (for on-chain data)
web3==6.12.0
eth-account==0.10.0

# =====================================================
# PRODUCTION DEPLOYMENT
# =====================================================

# Container and deployment
gunicorn==21.2.0
supervisor==4.2.5

# Load balancing and proxy
nginx==1.25.3  # Note: This is typically installed via system package manager

# =====================================================
# VERSION CONSTRAINTS
# =====================================================

# Ensure compatibility
python-version>=3.11,<3.13
