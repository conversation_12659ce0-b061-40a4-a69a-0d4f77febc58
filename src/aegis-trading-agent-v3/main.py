"""
Aegis Trading Agent v3 - Test Infrastructure
Minimal FastAPI application for Phase 1 deployment testing.
"""

import os
import sys
import logging
import traceback
from datetime import datetime
from fastapi import FastAPI, HTTPException
from pydantic import BaseModel
from typing import Dict, Any, List

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Create FastAPI app
app = FastAPI(
    title="Aegis Trading Agent v3",
    description="AI-powered cryptocurrency trading agent with Tyler-only access",
    version="3.0.0"
)

class ImportTestResult(BaseModel):
    module: str
    success: bool
    error: str = None
    version: str = None

class HealthResponse(BaseModel):
    status: str
    service: str
    version: str
    timestamp: str
    environment: str
    project_id: str

@app.get("/", response_model=HealthResponse)
async def root():
    """Health check endpoint."""
    return HealthResponse(
        status="healthy",
        service="aegis-trading-agent-v3",
        version="3.0.0",
        timestamp=datetime.utcnow().isoformat(),
        environment=os.getenv("ENVIRONMENT", "development"),
        project_id=os.getenv("GCP_PROJECT_ID", "vertex-ai-agent-yzdlnjey")
    )

@app.get("/health", response_model=HealthResponse)
async def health():
    """Detailed health check."""
    return await root()

@app.get("/test-imports")
async def test_imports():
    """
    Test import endpoint following agent template methodology.
    Tests minimal dependencies required for trading agent.
    """
    test_modules = [
        "fastapi",
        "uvicorn",
        "pydantic",
        "pydantic_settings",
        "requests",
        "python-dotenv",
        "google-cloud-secret-manager",
        "google-cloud-firestore",
        "redis"
    ]
    
    results = []
    
    for module_name in test_modules:
        result = ImportTestResult(module=module_name, success=False)
        
        try:
            # Handle module name variations
            import_name = module_name
            if module_name == "python-dotenv":
                import_name = "dotenv"
            elif module_name == "google-cloud-secret-manager":
                import_name = "google.cloud.secretmanager"
            elif module_name == "google-cloud-firestore":
                import_name = "google.cloud.firestore"
            
            module = __import__(import_name)
            result.success = True
            
            # Try to get version if available
            if hasattr(module, "__version__"):
                result.version = module.__version__
            elif hasattr(module, "version"):
                result.version = str(module.version)
                
        except ImportError as e:
            result.error = f"ImportError: {str(e)}"
        except Exception as e:
            result.error = f"Error: {str(e)}"
            
        results.append(result)
    
    # Summary
    successful_imports = sum(1 for r in results if r.success)
    total_imports = len(results)
    
    return {
        "status": "complete",
        "summary": {
            "successful": successful_imports,
            "total": total_imports,
            "success_rate": f"{(successful_imports/total_imports)*100:.1f}%"
        },
        "results": results,
        "timestamp": datetime.utcnow().isoformat(),
        "python_version": sys.version,
        "environment": os.getenv("ENVIRONMENT", "development")
    }

@app.get("/test-gcp-connectivity")
async def test_gcp_connectivity():
    """Test GCP service connectivity."""
    try:
        project_id = os.getenv("GCP_PROJECT_ID", "vertex-ai-agent-yzdlnjey")
        
        # Test basic GCP project access
        from google.cloud import secretmanager
        client = secretmanager.SecretManagerServiceClient()
        
        # Try to list secrets (this will validate authentication)
        parent = f"projects/{project_id}"
        secrets = list(client.list_secrets(request={"parent": parent}))
        
        # Filter for trading secrets
        trading_secrets = [s for s in secrets if "AEGIS_TRADING_" in s.name]
        
        return {
            "status": "success",
            "project_id": project_id,
            "total_secrets": len(secrets),
            "trading_secrets": len(trading_secrets),
            "trading_secret_names": [s.name.split("/")[-1] for s in trading_secrets],
            "timestamp": datetime.utcnow().isoformat()
        }
        
    except Exception as e:
        logger.error(f"GCP connectivity test failed: {str(e)}")
        return {
            "status": "error",
            "error": str(e),
            "traceback": traceback.format_exc(),
            "timestamp": datetime.utcnow().isoformat()
        }

@app.get("/test-service-account")
async def test_service_account():
    """Test service account authentication and permissions."""
    try:
        from google.auth import default
        from google.cloud import secretmanager
        
        # Get default credentials
        credentials, project = default()
        
        # Test Secret Manager access
        client = secretmanager.SecretManagerServiceClient(credentials=credentials)
        
        # Try to access a trading secret
        secret_name = f"projects/{project}/secrets/AEGIS_TRADING_COINGECKO_API_KEY"
        
        try:
            secret = client.get_secret(request={"name": secret_name})
            secret_access = True
        except Exception as e:
            secret_access = False
            secret_error = str(e)
        
        return {
            "status": "success",
            "project_id": project,
            "service_account": getattr(credentials, 'service_account_email', 'default'),
            "secret_access": secret_access,
            "secret_error": secret_error if not secret_access else None,
            "timestamp": datetime.utcnow().isoformat()
        }
        
    except Exception as e:
        logger.error(f"Service account test failed: {str(e)}")
        return {
            "status": "error",
            "error": str(e),
            "timestamp": datetime.utcnow().isoformat()
        }

@app.get("/test-phase2-imports")
async def test_phase2_imports():
    """Test Phase 2 dependencies for autonomous crypto research system."""
    phase2_modules = [
        "langchain",
        "langchain_core",
        "langchain_community",
        "langchain_google_vertexai",
        "langgraph",
        "numpy",
        "pandas",
        "sklearn",
        "googleapiclient",
        "apscheduler",
        "bs4",
        "feedparser"
    ]

    results = []

    for module_name in phase2_modules:
        result = ImportTestResult(module=module_name, success=False)

        try:
            # Handle module name variations
            import_name = module_name
            if module_name == "sklearn":
                import_name = "sklearn"
            elif module_name == "bs4":
                import_name = "bs4"
            elif module_name == "googleapiclient":
                import_name = "googleapiclient"

            module = __import__(import_name)
            result.success = True

            # Try to get version if available
            if hasattr(module, "__version__"):
                result.version = module.__version__
            elif hasattr(module, "version"):
                result.version = str(module.version)

        except ImportError as e:
            result.error = f"ImportError: {str(e)}"
        except Exception as e:
            result.error = f"Error: {str(e)}"

        results.append(result)

    # Summary
    successful_imports = sum(1 for r in results if r.success)
    total_imports = len(results)

    return {
        "status": "complete",
        "phase": "Phase 2: Progressive Dependencies",
        "summary": {
            "successful": successful_imports,
            "total": total_imports,
            "success_rate": f"{(successful_imports/total_imports)*100:.1f}%"
        },
        "results": results,
        "timestamp": datetime.utcnow().isoformat(),
        "python_version": sys.version,
        "environment": os.getenv("ENVIRONMENT", "development")
    }

@app.get("/test-phase3-imports")
async def test_phase3_imports():
    """Test Phase 3 imports for autonomous crypto research modules."""
    try:
        # Test core module imports
        from src.config import config, AegisConfig
        from src.crypto_apis import CryptoAPIManager, MarketData
        from src.analysis import TechnicalAnalyzer, MarketIntelligence, TradingSignal
        from src.alerts import EmailAlertManager
        from src.workflows import AutonomousResearchWorkflow

        results = {
            "status": "success",
            "phase": "Phase 3: Import Resolution & API Integration",
            "modules_tested": {
                "config": "✅ AegisConfig loaded successfully",
                "crypto_apis": "✅ CryptoAPIManager imported",
                "analysis": "✅ TechnicalAnalyzer and MarketIntelligence imported",
                "alerts": "✅ EmailAlertManager imported",
                "workflows": "✅ AutonomousResearchWorkflow imported"
            },
            "configuration": {
                "project_id": config.project_id,
                "environment": config.environment,
                "analysis_interval_hours": config.analysis_interval_hours,
                "confidence_threshold": config.confidence_threshold
            },
            "api_status": {
                "available_apis": ["coingecko", "dex_screener", "santiment", "newsapi", "cryptopanic"],
                "secrets_loaded": bool(config.coingecko_api_key)
            },
            "timestamp": datetime.utcnow().isoformat()
        }

        return results

    except Exception as e:
        return {
            "status": "error",
            "phase": "Phase 3: Import Resolution & API Integration",
            "error": str(e),
            "timestamp": datetime.utcnow().isoformat()
        }

@app.get("/test-crypto-apis")
async def test_crypto_apis():
    """Test cryptocurrency API connections and data retrieval."""
    try:
        from src.crypto_apis import CryptoAPIManager

        # Initialize API manager
        api_manager = CryptoAPIManager()

        # Test API availability
        available_apis = api_manager.get_available_apis()

        # Test basic functionality (without making actual API calls)
        test_results = {
            "status": "success",
            "available_apis": available_apis,
            "api_clients_initialized": {
                "coingecko": api_manager.coingecko is not None,
                "dex_screener": api_manager.dex_screener is not None,
                "santiment": api_manager.santiment is not None,
                "newsapi": api_manager.newsapi is not None,
                "cryptopanic": api_manager.cryptopanic is not None
            },
            "rate_limiters_configured": True,
            "timestamp": datetime.utcnow().isoformat()
        }

        return test_results

    except Exception as e:
        return {
            "status": "error",
            "error": str(e),
            "timestamp": datetime.utcnow().isoformat()
        }

@app.get("/test-analysis-engine")
async def test_analysis_engine():
    """Test technical analysis and market intelligence engines."""
    try:
        from src.analysis import TechnicalAnalyzer, MarketIntelligence, TradingSignal, SignalType, ConfidenceLevel
        from src.crypto_apis import MarketData

        # Initialize analyzers
        tech_analyzer = TechnicalAnalyzer()
        market_intel = MarketIntelligence()

        # Create test market data
        test_data = [
            MarketData(
                symbol="BTC",
                price=45000.0,
                volume_24h=1000000000,
                price_change_24h=2.5,
                market_cap=850000000000,
                source="test"
            )
        ] * 50  # Simulate historical data

        # Test technical analysis
        analysis = tech_analyzer.analyze_price_action(test_data)
        signal = tech_analyzer.generate_trading_signal("BTC", analysis)

        return {
            "status": "success",
            "technical_analysis": {
                "rsi": analysis.get("rsi"),
                "trend": analysis.get("trend"),
                "momentum": analysis.get("momentum")
            },
            "signal_generated": signal is not None,
            "signal_details": {
                "type": signal.signal_type.value if signal else None,
                "confidence": signal.confidence.value if signal else None,
                "confidence_score": signal.confidence_score if signal else None
            } if signal else None,
            "analyzers_initialized": True,
            "timestamp": datetime.utcnow().isoformat()
        }

    except Exception as e:
        return {
            "status": "error",
            "error": str(e),
            "timestamp": datetime.utcnow().isoformat()
        }

@app.get("/test-email-system")
async def test_email_system():
    """Test email alert system functionality."""
    try:
        from src.alerts import EmailAlertManager

        # Initialize email manager
        email_manager = EmailAlertManager()

        # Test email system
        test_result = email_manager.test_email_system()

        return {
            "status": "success",
            "email_manager_initialized": True,
            "test_result": test_result,
            "tyler_email": email_manager.tyler_email,
            "from_email": email_manager.from_email,
            "timestamp": datetime.utcnow().isoformat()
        }

    except Exception as e:
        return {
            "status": "error",
            "error": str(e),
            "timestamp": datetime.utcnow().isoformat()
        }

@app.get("/test-autonomous-workflow")
async def test_autonomous_workflow():
    """Test autonomous research workflow initialization."""
    try:
        from src.workflows import AutonomousResearchWorkflow

        # Initialize workflow
        workflow = AutonomousResearchWorkflow()

        # Get workflow status
        status = workflow.get_status()

        return {
            "status": "success",
            "workflow_initialized": True,
            "workflow_status": status,
            "langgraph_workflow_compiled": status.get("workflow_initialized", False),
            "vertex_ai_configured": True,
            "timestamp": datetime.utcnow().isoformat()
        }

    except Exception as e:
        return {
            "status": "error",
            "error": str(e),
            "timestamp": datetime.utcnow().isoformat()
        }

@app.get("/test-live-data")
async def test_live_data():
    """Test live cryptocurrency data retrieval from configured APIs."""
    try:
        from src.crypto_apis import CryptoAPIManager

        # Initialize API manager with production keys
        api_manager = CryptoAPIManager()

        # Test symbols
        test_symbols = ["bitcoin", "ethereum", "solana"]

        # Get available APIs
        available_apis = api_manager.get_available_apis()

        results = {
            "status": "success",
            "available_apis": available_apis,
            "test_symbols": test_symbols,
            "data_sources": {},
            "timestamp": datetime.utcnow().isoformat()
        }

        # Test each available API
        if "coingecko" in available_apis:
            try:
                coingecko_data = await api_manager.coingecko.get_market_data(test_symbols)
                results["data_sources"]["coingecko"] = {
                    "status": "success",
                    "data_points": len(coingecko_data),
                    "sample_data": coingecko_data[:2] if coingecko_data else []
                }
            except Exception as e:
                results["data_sources"]["coingecko"] = {
                    "status": "error",
                    "error": str(e)
                }

        if "newsapi" in available_apis:
            try:
                news_data = await api_manager.newsapi.get_crypto_news(["bitcoin", "ethereum", "cryptocurrency"])
                results["data_sources"]["newsapi"] = {
                    "status": "success",
                    "articles_count": len(news_data),
                    "sample_headlines": [article.title for article in news_data[:2]]
                }
            except Exception as e:
                results["data_sources"]["newsapi"] = {
                    "status": "error",
                    "error": str(e)
                }

        if "cryptopanic" in available_apis:
            try:
                panic_data = await api_manager.cryptopanic.get_news_feed(["bitcoin", "ethereum"])
                results["data_sources"]["cryptopanic"] = {
                    "status": "success",
                    "news_count": len(panic_data),
                    "sample_titles": [item.title for item in panic_data[:2]]
                }
            except Exception as e:
                results["data_sources"]["cryptopanic"] = {
                    "status": "error",
                    "error": str(e)
                }

        # Test DEX Screener (no API key required)
        try:
            if api_manager.dex_screener:
                dex_data = await api_manager.dex_screener.get_trending_pairs()
                results["data_sources"]["dex_screener"] = {
                    "status": "success",
                    "pairs_count": len(dex_data),
                    "sample_pairs": [f"{pair.base_token}/{pair.quote_token}" for pair in dex_data[:2]]
                }
            else:
                results["data_sources"]["dex_screener"] = {
                    "status": "not_initialized",
                    "message": "DEX Screener client not initialized"
                }
        except Exception as e:
            results["data_sources"]["dex_screener"] = {
                "status": "error",
                "error": str(e)
            }

        return results

    except Exception as e:
        return {
            "status": "error",
            "error": str(e),
            "timestamp": datetime.utcnow().isoformat()
        }

@app.get("/run-analysis-cycle")
async def run_analysis_cycle():
    """Run a complete autonomous analysis cycle (for Cloud Scheduler)."""
    try:
        from src.workflows import AutonomousResearchWorkflow

        # Initialize workflow
        workflow = AutonomousResearchWorkflow()

        # Run complete analysis cycle
        result = await workflow.run_analysis_cycle()

        return {
            "status": "success",
            "cycle_result": result,
            "timestamp": datetime.utcnow().isoformat()
        }

    except Exception as e:
        return {
            "status": "error",
            "error": str(e),
            "timestamp": datetime.utcnow().isoformat()
        }

@app.get("/run-macro-scan")
async def run_macro_scan():
    """Run hourly macro market scan (for Cloud Scheduler)."""
    try:
        from src.macro_scanner import MacroMarketScanner

        # Initialize macro scanner
        scanner = MacroMarketScanner()

        # Run macro scan
        result = await scanner.run_macro_scan()

        return {
            "status": "success",
            "scan_result": {
                "scan_type": result.scan_type,
                "total_cryptos_analyzed": result.total_cryptos_analyzed,
                "opportunity_score": float(result.opportunity_score) if result.opportunity_score else 0,
                "opportunities_detected": result.opportunities_detected,
                "triggers_activated": result.triggers_activated,
                "execution_time_seconds": float(result.execution_time_seconds) if result.execution_time_seconds else 0
            },
            "timestamp": datetime.utcnow().isoformat()
        }

    except Exception as e:
        return {
            "status": "error",
            "error": str(e),
            "timestamp": datetime.utcnow().isoformat()
        }

@app.get("/test-database-connection")
async def test_database_connection():
    """Test basic database connection."""
    try:
        import asyncpg
        from google.cloud import secretmanager

        # Get database credentials
        client = secretmanager.SecretManagerServiceClient()
        project_id = "vertex-ai-agent-yzdlnjey"

        # Retrieve database credentials
        host_secret = client.access_secret_version(
            request={"name": f"projects/{project_id}/secrets/AEGIS_TRADING_DATABASE_HOST/versions/latest"}
        )
        db_host = host_secret.payload.data.decode("UTF-8").strip()

        name_secret = client.access_secret_version(
            request={"name": f"projects/{project_id}/secrets/AEGIS_TRADING_DATABASE_NAME/versions/latest"}
        )
        db_name = name_secret.payload.data.decode("UTF-8").strip()

        user_secret = client.access_secret_version(
            request={"name": f"projects/{project_id}/secrets/AEGIS_TRADING_DATABASE_USER/versions/latest"}
        )
        db_user = user_secret.payload.data.decode("UTF-8").strip()

        password_secret = client.access_secret_version(
            request={"name": f"projects/{project_id}/secrets/AEGIS_TRADING_DATABASE_PASSWORD/versions/latest"}
        )
        db_password = password_secret.payload.data.decode("UTF-8").strip()

        # Test connection
        if db_host.startswith("/cloudsql/"):
            # Unix socket connection for Cloud SQL
            connection = await asyncpg.connect(
                host=db_host,
                database=db_name,
                user=db_user,
                password=db_password
            )
        else:
            # TCP connection
            connection = await asyncpg.connect(
                host=db_host,
                port=5432,
                database=db_name,
                user=db_user,
                password=db_password,
                ssl="require"
            )

        # Test query
        result = await connection.fetchval("SELECT version()")
        await connection.close()

        return {
            "status": "success",
            "message": "Database connection successful",
            "database_version": result,
            "host": db_host,
            "database": db_name,
            "user": db_user,
            "timestamp": datetime.utcnow().isoformat()
        }

    except Exception as e:
        return {
            "status": "error",
            "error": str(e),
            "timestamp": datetime.utcnow().isoformat()
        }

@app.get("/initialize-database")
async def initialize_database():
    """Initialize database schema (one-time setup)."""
    try:
        from src.database_init import initialize_database

        # Initialize database schema
        success = await initialize_database()

        if success:
            return {
                "status": "success",
                "message": "Database schema initialized successfully",
                "timestamp": datetime.utcnow().isoformat()
            }
        else:
            return {
                "status": "error",
                "message": "Database schema initialization failed",
                "timestamp": datetime.utcnow().isoformat()
            }

    except Exception as e:
        return {
            "status": "error",
            "error": str(e),
            "timestamp": datetime.utcnow().isoformat()
        }

@app.get("/start-historical-collection")
async def start_historical_collection(backfill_days: int = 7):
    """Start historical data collection service."""
    try:
        from src.historical_data_collector import HistoricalDataCollector

        # Initialize collector
        collector = HistoricalDataCollector()

        # Start collection in background
        import asyncio
        asyncio.create_task(collector.start_collection(backfill_days))

        return {
            "status": "success",
            "message": f"Historical data collection started with {backfill_days} days backfill",
            "timestamp": datetime.utcnow().isoformat()
        }

    except Exception as e:
        return {
            "status": "error",
            "error": str(e),
            "timestamp": datetime.utcnow().isoformat()
        }

@app.get("/collection-status")
async def get_collection_status():
    """Get status of historical data collection."""
    try:
        from src.historical_data_collector import HistoricalDataCollector

        # Get collection status
        collector = HistoricalDataCollector()
        status = await collector.get_collection_status()

        return {
            "status": "success",
            "collection_status": status,
            "timestamp": datetime.utcnow().isoformat()
        }

    except Exception as e:
        return {
            "status": "error",
            "error": str(e),
            "timestamp": datetime.utcnow().isoformat()
        }

@app.get("/send-test-email")
async def send_test_email():
    """Send a test email to Tyler to validate Gmail API integration."""
    try:
        from src.alerts import EmailAlertManager

        # Initialize email manager
        email_manager = EmailAlertManager()

        # Send test email
        result = await email_manager.send_test_email()

        return result

    except Exception as e:
        return {
            "status": "error",
            "error": str(e),
            "timestamp": datetime.utcnow().isoformat()
        }

if __name__ == "__main__":
    import uvicorn
    port = int(os.getenv("PORT", 8080))
    logger.info(f"Starting Aegis Trading Agent v3 on port {port}")
    uvicorn.run(app, host="0.0.0.0", port=port)
