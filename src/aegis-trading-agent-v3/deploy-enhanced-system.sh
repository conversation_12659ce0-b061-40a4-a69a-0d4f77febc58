#!/bin/bash

# Aegis Trading Agent v3 - Enhanced System Deployment Script
# Deploy the enhanced intelligence system to Cloud Run
# Tyler-Only Access - Complete Isolation from Business Systems

set -e

echo "🚀 Deploying Aegis Trading Agent v3 Enhanced Intelligence System"
echo "=================================================="

# Set GCP project
PROJECT_ID="vertex-ai-agent-yzdlnjey"
SERVICE_NAME="aegis-trading-agent"
REGION="us-west1"

echo "📋 Setting GCP project to $PROJECT_ID"
gcloud config set project $PROJECT_ID

echo "🔧 Building and deploying enhanced system..."

# Deploy to Cloud Run with enhanced configuration
gcloud run deploy $SERVICE_NAME \
    --source . \
    --platform managed \
    --region $REGION \
    --allow-unauthenticated \
    --service-account="aegis-trading-agent-sa@$PROJECT_ID.iam.gserviceaccount.com" \
    --memory="2Gi" \
    --cpu="2" \
    --timeout="3600" \
    --concurrency="10" \
    --max-instances="5" \
    --set-env-vars="ENVIRONMENT=production,GCP_PROJECT_ID=$PROJECT_ID" \
    --set-env-vars="AEGIS_TRADING_ENABLE_MACRO_SCANNING=true" \
    --set-env-vars="AEGIS_TRADING_ENABLE_PREDICTIVE_ANALYSIS=true" \
    --set-env-vars="AEGIS_TRADING_ENABLE_BACKTESTING=true" \
    --set-env-vars="AEGIS_TRADING_MAX_CRYPTOCURRENCIES_MACRO_SCAN=100" \
    --project=$PROJECT_ID

echo "✅ Enhanced system deployed successfully!"

# Get the service URL
SERVICE_URL=$(gcloud run services describe $SERVICE_NAME --region=$REGION --format="value(status.url)" --project=$PROJECT_ID)
echo "🌐 Service URL: $SERVICE_URL"

echo ""
echo "🔧 Testing enhanced endpoints..."

# Test database initialization
echo "📊 Testing database initialization..."
curl -s "$SERVICE_URL/initialize-database" | jq '.'

echo ""
echo "📈 Testing macro scan endpoint..."
curl -s "$SERVICE_URL/run-macro-scan" | jq '.'

echo ""
echo "📊 Testing collection status..."
curl -s "$SERVICE_URL/collection-status" | jq '.'

echo ""
echo "🎯 Enhanced system deployment completed!"
echo "=================================================="
echo ""
echo "📋 Next Steps:"
echo "1. Initialize database schema: GET $SERVICE_URL/initialize-database"
echo "2. Start historical data collection: GET $SERVICE_URL/start-historical-collection"
echo "3. Monitor collection status: GET $SERVICE_URL/collection-status"
echo "4. Test macro scanning: GET $SERVICE_URL/run-macro-scan"
echo ""
echo "🔄 The system will automatically:"
echo "- Collect historical data for 60+ cryptocurrencies"
echo "- Run hourly macro market scans (when scheduler is enabled)"
echo "- Store all data in PostgreSQL with TimescaleDB optimization"
echo "- Continue the existing 4-hour analysis cycle"
echo ""
echo "✅ Aegis Trading Agent v3 Enhanced Intelligence System is ready!"
