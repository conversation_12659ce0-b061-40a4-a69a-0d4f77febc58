"""
Test Script for YouTube Integration

This script tests the YouTube Data API integration for content monitoring
and influencer identification capabilities.
"""

import asyncio
import logging
import sys
import os
from datetime import datetime

# Add the project root to the path
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..'))

from src.Marketing_Agent.tools.youtube_integration import (
    monitor_youtube_content,
    analyze_youtube_video_engagement,
    YouTubeAPIClient,
    CRYPTO_YOUTUBE_KEYWORDS,
    COMPETITOR_CHANNELS
)

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


async def test_youtube_monitoring_mock():
    """Test YouTube monitoring with mock data (no API key required)."""
    logger.info("Testing YouTube monitoring with mock data...")
    
    try:
        result = await monitor_youtube_content.ainvoke({
            "keywords": ["bitcoin atm", "crypto fees"],
            "max_videos_per_keyword": 10,
            "time_filter": "week",
            "api_key": None  # This will trigger mock data
        })
        
        if result and "total_videos_analyzed" in result:
            logger.info(f"✅ YouTube monitoring successful (mock data)")
            logger.info(f"Videos analyzed: {result['total_videos_analyzed']}")
            logger.info(f"Engagement opportunities: {result['engagement_opportunities']}")
            logger.info(f"Potential influencers: {result['potential_influencers']}")
            
            # Show top opportunities
            if result["top_opportunities"]:
                logger.info("Top engagement opportunities:")
                for i, opp in enumerate(result["top_opportunities"][:2], 1):
                    logger.info(f"{i}. {opp['title'][:50]}...")
                    logger.info(f"   Channel: {opp['channel_title']}")
                    logger.info(f"   Score: {opp['opportunity_score']}/10")
                    logger.info(f"   Reasons: {', '.join(opp['opportunity_reasons'])}")
            
            return True
        else:
            logger.error(f"❌ YouTube monitoring failed: {result}")
            return False
            
    except Exception as e:
        logger.error(f"❌ YouTube monitoring test failed: {e}")
        return False


async def test_video_analysis_mock():
    """Test individual video analysis with mock data."""
    logger.info("Testing YouTube video analysis with mock data...")
    
    try:
        analysis = await analyze_youtube_video_engagement.ainvoke({
            "video_id": "mock_video_123",
            "api_key": None  # This will trigger mock analysis
        })
        
        if analysis and "engagement_potential" in analysis:
            logger.info(f"✅ Video analysis successful (mock data)")
            logger.info(f"Video ID: {analysis['video_id']}")
            logger.info(f"Engagement potential: {analysis['engagement_potential']}")
            logger.info(f"Key themes: {analysis['key_themes']}")
            logger.info(f"Comment strategy: {analysis['comment_strategy']}")
            logger.info(f"Risk level: {analysis['risk_level']}")
            
            return True
        else:
            logger.error(f"❌ Video analysis failed: {analysis}")
            return False
            
    except Exception as e:
        logger.error(f"❌ Video analysis test failed: {e}")
        return False


async def test_keyword_effectiveness():
    """Test effectiveness of cryptocurrency keywords for YouTube."""
    logger.info("Testing YouTube keyword effectiveness...")
    
    try:
        keyword_results = {}
        
        # Test with mock data for different keywords
        for keyword in CRYPTO_YOUTUBE_KEYWORDS[:3]:
            result = await monitor_youtube_content.ainvoke({
                "keywords": [keyword],
                "max_videos_per_keyword": 5,
                "time_filter": "week",
                "api_key": None
            })
            
            if result and "total_videos_analyzed" in result:
                keyword_results[keyword] = result["engagement_opportunities"]
                logger.info(f"Keyword '{keyword}': {result['engagement_opportunities']} opportunities")
            else:
                keyword_results[keyword] = 0
                logger.warning(f"Keyword '{keyword}': No results")
        
        total_opportunities = sum(keyword_results.values())
        logger.info(f"Keyword effectiveness test complete:")
        logger.info(f"Total opportunities across keywords: {total_opportunities}")
        logger.info(f"Keyword results: {keyword_results}")
        
        return total_opportunities > 0
        
    except Exception as e:
        logger.error(f"❌ Keyword effectiveness test failed: {e}")
        return False


async def test_competitor_monitoring():
    """Test competitor channel monitoring capabilities."""
    logger.info("Testing competitor monitoring...")
    
    try:
        # Test monitoring for competitor mentions
        competitor_keywords = ["bitcoin depot", "coinstar bitcoin", "coinme"]
        
        result = await monitor_youtube_content.ainvoke({
            "keywords": competitor_keywords,
            "max_videos_per_keyword": 8,
            "time_filter": "week",
            "api_key": None
        })
        
        if result and "top_opportunities" in result:
            competitor_mentions = 0
            for opp in result["top_opportunities"]:
                if "competitor_mention" in opp.get("opportunity_reasons", []):
                    competitor_mentions += 1
            
            logger.info(f"✅ Competitor monitoring successful")
            logger.info(f"Total opportunities: {result['engagement_opportunities']}")
            logger.info(f"Competitor mentions found: {competitor_mentions}")
            
            return True
        else:
            logger.error(f"❌ Competitor monitoring failed: {result}")
            return False
            
    except Exception as e:
        logger.error(f"❌ Competitor monitoring test failed: {e}")
        return False


async def test_influencer_identification():
    """Test influencer identification capabilities."""
    logger.info("Testing influencer identification...")
    
    try:
        result = await monitor_youtube_content.ainvoke({
            "keywords": ["bitcoin tutorial", "crypto education"],
            "max_videos_per_keyword": 15,
            "time_filter": "month",
            "api_key": None
        })
        
        if result and "top_influencers" in result:
            logger.info(f"✅ Influencer identification successful")
            logger.info(f"Potential influencers found: {result['potential_influencers']}")
            
            if result["top_influencers"]:
                logger.info("Top influencers:")
                for i, influencer in enumerate(result["top_influencers"][:3], 1):
                    logger.info(f"{i}. {influencer['channel_title']}")
                    logger.info(f"   Views: {influencer['view_count']:,}")
                    logger.info(f"   Engagement: {influencer['engagement_rate']}%")
            
            return True
        else:
            logger.error(f"❌ Influencer identification failed: {result}")
            return False
            
    except Exception as e:
        logger.error(f"❌ Influencer identification test failed: {e}")
        return False


async def run_youtube_integration_demo():
    """Run a comprehensive demo of YouTube integration capabilities."""
    logger.info("🚀 Running YouTube Integration Demo...")
    logger.info("=" * 60)
    
    try:
        # Simulate a real monitoring session
        demo_result = await monitor_youtube_content.ainvoke({
            "keywords": ["bitcoin atm", "crypto fees", "coinme"],
            "max_videos_per_keyword": 10,
            "time_filter": "week",
            "api_key": None  # Using mock data for demo
        })
        
        if demo_result and demo_result.get("total_videos_analyzed", 0) > 0:
            logger.info("🎯 YOUTUBE INTEGRATION DEMO RESULTS:")
            logger.info("=" * 50)
            
            logger.info(f"📊 Total Videos Analyzed: {demo_result['total_videos_analyzed']}")
            logger.info(f"🎯 Engagement Opportunities: {demo_result['engagement_opportunities']}")
            logger.info(f"👥 Potential Influencers: {demo_result['potential_influencers']}")
            logger.info(f"🔍 Keywords Searched: {', '.join(demo_result['keywords_searched'])}")
            
            if demo_result["top_opportunities"]:
                logger.info("\n🎯 TOP ENGAGEMENT OPPORTUNITIES:")
                for i, opp in enumerate(demo_result["top_opportunities"][:3], 1):
                    logger.info(f"{i}. {opp['title'][:60]}...")
                    logger.info(f"   Channel: {opp['channel_title']}")
                    logger.info(f"   Views: {opp['view_count']:,}")
                    logger.info(f"   Comments: {opp['comment_count']}")
                    logger.info(f"   Engagement Rate: {opp['engagement_rate']}%")
                    logger.info(f"   Opportunity Score: {opp['opportunity_score']}/10")
                    logger.info(f"   Reasons: {', '.join(opp['opportunity_reasons'])}")
                    logger.info("")
            
            if demo_result["top_influencers"]:
                logger.info("👥 TOP INFLUENCERS:")
                for i, inf in enumerate(demo_result["top_influencers"][:3], 1):
                    logger.info(f"{i}. {inf['channel_title']}")
                    logger.info(f"   Latest Video: {inf['video_title'][:50]}...")
                    logger.info(f"   Views: {inf['view_count']:,}")
                    logger.info(f"   Engagement Rate: {inf['engagement_rate']}%")
                    logger.info("")
            
            logger.info("✅ YouTube integration demo completed successfully!")
            return True
        else:
            logger.warning("⚠️ Demo completed but no content found")
            return False
            
    except Exception as e:
        logger.error(f"❌ YouTube integration demo failed: {e}")
        return False


async def test_api_key_validation():
    """Test API key validation and error handling."""
    logger.info("Testing API key validation...")
    
    try:
        # Test with invalid API key
        result = await monitor_youtube_content.ainvoke({
            "keywords": ["bitcoin"],
            "max_videos_per_keyword": 5,
            "time_filter": "day",
            "api_key": "invalid_key_123"
        })
        
        # Should handle gracefully and return error or mock data
        if result:
            logger.info("✅ API key validation handled gracefully")
            if "error" in result:
                logger.info(f"Expected error handling: {result['error']}")
            elif "note" in result:
                logger.info(f"Fallback to mock data: {result['note']}")
            return True
        else:
            logger.error("❌ API key validation failed")
            return False
            
    except Exception as e:
        logger.error(f"❌ API key validation test failed: {e}")
        return False


async def main():
    """Run all YouTube integration tests."""
    logger.info("🧪 Starting YouTube Integration Tests")
    logger.info("=" * 60)
    
    tests = [
        ("YouTube Monitoring (Mock)", test_youtube_monitoring_mock),
        ("Video Analysis (Mock)", test_video_analysis_mock),
        ("Keyword Effectiveness", test_keyword_effectiveness),
        ("Competitor Monitoring", test_competitor_monitoring),
        ("Influencer Identification", test_influencer_identification),
        ("API Key Validation", test_api_key_validation),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        logger.info(f"\n🧪 Running {test_name} test...")
        try:
            if await test_func():
                passed += 1
                logger.info(f"✅ {test_name} test passed")
            else:
                logger.error(f"❌ {test_name} test failed")
        except Exception as e:
            logger.error(f"❌ {test_name} test error: {e}")
    
    logger.info(f"\n📊 TEST RESULTS: {passed}/{total} tests passed")
    
    if passed == total:
        logger.info("🎉 All tests passed! Running integration demo...")
        await run_youtube_integration_demo()
        logger.info("\n✅ YouTube integration is ready for production!")
        logger.info("\n📝 NOTE: For live YouTube monitoring, you'll need:")
        logger.info("   1. Google Cloud Project with YouTube Data API v3 enabled")
        logger.info("   2. API key stored in Secret Manager")
        logger.info("   3. Quota management for API calls")
    else:
        logger.error("❌ Some tests failed. Please fix issues before deployment.")
    
    return passed == total


if __name__ == "__main__":
    asyncio.run(main())
