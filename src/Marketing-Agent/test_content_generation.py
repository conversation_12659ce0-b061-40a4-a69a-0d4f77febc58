"""
Test Script for Multi-Platform Content Generation System

This script tests the comprehensive content generation capabilities
for Bitcoin ATM marketing across multiple platforms and formats.
"""

import asyncio
import logging
import sys
import os
from datetime import datetime

# Add the project root to the path
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..'))

from src.Marketing_Agent.tools.content_generation import (
    generate_marketing_content,
    create_content_campaign,
    ab_test_content_variations,
    list_content_templates,
    CONTENT_TEMPLATES
)

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


async def test_template_listing():
    """Test listing of available content templates."""
    logger.info("Testing content template listing...")
    
    try:
        result = await list_content_templates.ainvoke({})
        
        if result and "templates" in result:
            logger.info(f"✅ Template listing successful")
            logger.info(f"Total templates: {result['total_templates']}")
            logger.info(f"Content types: {', '.join(result['content_types'])}")
            logger.info(f"Platforms: {', '.join(result['platforms'])}")
            
            # Show sample templates
            logger.info("Available templates:")
            for name, template in list(result["templates"].items())[:3]:
                logger.info(f"  - {name}: {template['platform']} ({template['content_type']})")
            
            return True
        else:
            logger.error(f"❌ Template listing failed: {result}")
            return False
            
    except Exception as e:
        logger.error(f"❌ Template listing test failed: {e}")
        return False


async def test_single_content_generation():
    """Test generation of a single piece of content."""
    logger.info("Testing single content generation...")
    
    try:
        result = await generate_marketing_content.ainvoke({
            "template_name": "reddit_educational_post",
            "topic": "How Bitcoin ATMs Work",
            "key_points": [
                "Simple cash-to-Bitcoin process",
                "No bank account required",
                "Available 24/7",
                "Secure transactions"
            ],
            "target_audience": "Cryptocurrency newcomers",
            "custom_instructions": "Focus on addressing common concerns about security"
        })
        
        if result and result.get("success"):
            logger.info(f"✅ Content generation successful")
            logger.info(f"Content ID: {result['content_id']}")
            logger.info(f"Title: {result['title']}")
            logger.info(f"Platform: {result['platform']}")
            logger.info(f"Word count: {result['word_count']}")
            logger.info(f"Character count: {result['character_count']}")
            logger.info(f"Compliance: {result['compliance_status']}")
            logger.info(f"Model used: {result['model_used']}")
            logger.info(f"Hashtags: {', '.join(result['hashtags'])}")
            
            return True
        else:
            logger.error(f"❌ Content generation failed: {result}")
            return False
            
    except Exception as e:
        logger.error(f"❌ Content generation test failed: {e}")
        return False


async def test_content_campaign_creation():
    """Test creation of a multi-platform content campaign."""
    logger.info("Testing content campaign creation...")
    
    try:
        campaign_templates = [
            "reddit_educational_post",
            "youtube_comment_response",
            "blog_post_educational",
            "email_newsletter",
            "social_media_promo"
        ]
        
        campaign_topics = [
            "Bitcoin ATM Security Features",
            "Getting Started with Bitcoin",
            "Benefits of Using Bitcoin ATMs",
            "Cryptocurrency Made Simple",
            "Find Bitcoin ATMs Near You"
        ]
        
        result = await create_content_campaign.ainvoke({
            "campaign_name": "Q1 2024 Educational Campaign",
            "template_names": campaign_templates,
            "topics": campaign_topics,
            "target_audiences": [
                "Security-conscious users",
                "YouTube crypto community",
                "General public",
                "Email subscribers",
                "Social media followers"
            ]
        })
        
        if result and "campaign_id" in result:
            logger.info(f"✅ Campaign creation successful")
            logger.info(f"Campaign ID: {result['campaign_id']}")
            logger.info(f"Campaign name: {result['campaign_name']}")
            logger.info(f"Total content pieces: {result['total_content_pieces']}")
            logger.info(f"Successful generations: {result['successful_generations']}")
            
            # Show generated content
            logger.info("Generated content pieces:")
            for i, content in enumerate(result["content_pieces"][:3], 1):
                if content.get("success"):
                    logger.info(f"  {i}. {content['title'][:50]}... ({content['platform']})")
            
            return True
        else:
            logger.error(f"❌ Campaign creation failed: {result}")
            return False
            
    except Exception as e:
        logger.error(f"❌ Campaign creation test failed: {e}")
        return False


async def test_ab_testing_variations():
    """Test A/B testing content variations."""
    logger.info("Testing A/B content variations...")
    
    try:
        result = await ab_test_content_variations.ainvoke({
            "template_name": "social_media_promo",
            "topic": "Bitcoin ATM Convenience",
            "key_points": [
                "Instant transactions",
                "No waiting periods",
                "Cash to Bitcoin in minutes",
                "Thousands of locations"
            ],
            "variation_count": 3,
            "target_audience": "Busy professionals"
        })
        
        if result and "variations" in result:
            logger.info(f"✅ A/B testing successful")
            logger.info(f"Test ID: {result['test_id']}")
            logger.info(f"Total variations: {result['total_variations']}")
            logger.info(f"Template used: {result['template_used']}")
            
            # Show variations
            logger.info("Generated variations:")
            for i, variation in enumerate(result["variations"], 1):
                logger.info(f"  Variation {i}: {variation['variation_focus']}")
                logger.info(f"    Model: {variation['model_used']}")
                logger.info(f"    Length: {variation['character_count']} chars")
            
            logger.info("Testing recommendations:")
            for rec in result["testing_recommendations"]:
                logger.info(f"  • {rec}")
            
            return True
        else:
            logger.error(f"❌ A/B testing failed: {result}")
            return False
            
    except Exception as e:
        logger.error(f"❌ A/B testing test failed: {e}")
        return False


async def test_platform_specific_content():
    """Test content generation for different platforms."""
    logger.info("Testing platform-specific content...")
    
    try:
        platforms_to_test = [
            ("reddit_educational_post", "Reddit"),
            ("youtube_comment_response", "YouTube"),
            ("email_newsletter", "Email"),
            ("social_media_promo", "Social Media")
        ]
        
        successful_platforms = 0
        
        for template_name, platform_name in platforms_to_test:
            result = await generate_marketing_content.ainvoke({
                "template_name": template_name,
                "topic": f"Bitcoin ATMs for {platform_name}",
                "key_points": ["Easy to use", "Secure", "Convenient"],
                "target_audience": f"{platform_name} users"
            })
            
            if result and result.get("success"):
                successful_platforms += 1
                logger.info(f"✅ {platform_name} content successful: {result['character_count']} chars")
            else:
                logger.error(f"❌ {platform_name} content failed")
        
        logger.info(f"Platform testing: {successful_platforms}/{len(platforms_to_test)} platforms successful")
        return successful_platforms == len(platforms_to_test)
        
    except Exception as e:
        logger.error(f"❌ Platform testing failed: {e}")
        return False


async def test_model_comparison():
    """Test content generation with different AI models."""
    logger.info("Testing model comparison (Gemini vs Claude)...")
    
    try:
        topic = "Why Choose Bitcoin ATMs"
        key_points = ["Speed", "Security", "Simplicity", "Accessibility"]
        
        # Generate with Gemini
        gemini_result = await generate_marketing_content.ainvoke({
            "template_name": "blog_post_educational",
            "topic": topic,
            "key_points": key_points,
            "target_audience": "General public",
            "use_claude": False
        })
        
        # Generate with Claude (mock)
        claude_result = await generate_marketing_content.ainvoke({
            "template_name": "blog_post_educational",
            "topic": topic,
            "key_points": key_points,
            "target_audience": "General public",
            "use_claude": True
        })
        
        if gemini_result.get("success") and claude_result.get("success"):
            logger.info(f"✅ Model comparison successful")
            logger.info(f"Gemini content: {gemini_result['word_count']} words, {gemini_result['model_used']}")
            logger.info(f"Claude content: {claude_result['word_count']} words, {claude_result['model_used']}")
            
            # Compare characteristics
            logger.info("Content comparison:")
            logger.info(f"  Gemini hashtags: {len(gemini_result['hashtags'])}")
            logger.info(f"  Claude hashtags: {len(claude_result['hashtags'])}")
            logger.info(f"  Gemini compliance: {gemini_result['compliance_status']}")
            logger.info(f"  Claude compliance: {claude_result['compliance_status']}")
            
            return True
        else:
            logger.error("❌ Model comparison failed")
            return False
            
    except Exception as e:
        logger.error(f"❌ Model comparison test failed: {e}")
        return False


async def test_compliance_checking():
    """Test content compliance validation."""
    logger.info("Testing compliance checking...")
    
    try:
        # Test with compliant content
        compliant_result = await generate_marketing_content.ainvoke({
            "template_name": "reddit_educational_post",
            "topic": "Understanding Bitcoin ATM Fees",
            "key_points": ["Transparent pricing", "Service convenience", "Security features"],
            "target_audience": "Cost-conscious users"
        })
        
        # Test with potentially problematic content
        risky_result = await generate_marketing_content.ainvoke({
            "template_name": "social_media_promo",
            "topic": "Guaranteed Bitcoin Profits",
            "key_points": ["Risk-free investment", "Guaranteed returns", "Investment advice"],
            "target_audience": "Investors",
            "custom_instructions": "Promise high returns and no risk"
        })
        
        if compliant_result.get("success") and risky_result.get("success"):
            logger.info(f"✅ Compliance checking successful")
            logger.info(f"Compliant content status: {compliant_result['compliance_status']}")
            logger.info(f"Risky content status: {risky_result['compliance_status']}")
            
            # Check if compliance system caught issues
            if "compliant" in compliant_result['compliance_status']:
                logger.info("✅ Compliant content properly validated")
            
            if "issues" in risky_result['compliance_status']:
                logger.info("✅ Compliance issues properly detected")
            
            return True
        else:
            logger.error("❌ Compliance checking failed")
            return False
            
    except Exception as e:
        logger.error(f"❌ Compliance checking test failed: {e}")
        return False


async def run_content_generation_demo():
    """Run a comprehensive demo of content generation capabilities."""
    logger.info("🚀 Running Content Generation Demo...")
    logger.info("=" * 60)
    
    try:
        # Demo: Create a complete marketing campaign
        logger.info("📝 CREATING COMPLETE MARKETING CAMPAIGN")
        logger.info("=" * 50)
        
        campaign_result = await create_content_campaign.ainvoke({
            "campaign_name": "Coinme Q1 2024 Omnichannel Campaign",
            "template_names": [
                "blog_post_educational",
                "reddit_educational_post",
                "youtube_comment_response",
                "email_newsletter",
                "social_media_promo"
            ],
            "topics": [
                "Complete Guide to Bitcoin ATMs",
                "Bitcoin ATM vs Exchange: Which is Better?",
                "Common Bitcoin ATM Questions Answered",
                "Your Monthly Crypto Update",
                "Bitcoin ATMs: Now in Your Neighborhood"
            ],
            "target_audiences": [
                "Crypto beginners",
                "Reddit crypto community",
                "YouTube viewers",
                "Email subscribers",
                "Local community"
            ]
        })
        
        if campaign_result and "campaign_id" in campaign_result:
            logger.info("🎯 CAMPAIGN DEMO RESULTS:")
            logger.info("=" * 40)
            
            logger.info(f"📊 Campaign ID: {campaign_result['campaign_id']}")
            logger.info(f"📝 Total Content: {campaign_result['total_content_pieces']}")
            logger.info(f"✅ Successful: {campaign_result['successful_generations']}")
            logger.info(f"🎯 Templates: {', '.join(campaign_result['templates_used'])}")
            
            logger.info("\n📝 GENERATED CONTENT:")
            for i, content in enumerate(campaign_result["content_pieces"], 1):
                if content.get("success"):
                    logger.info(f"{i}. {content['title']}")
                    logger.info(f"   Platform: {content['platform']} | Type: {content['content_type']}")
                    logger.info(f"   Length: {content['word_count']} words | Model: {content['model_used']}")
                    logger.info(f"   Compliance: {content['compliance_status']}")
                    logger.info("")
            
            # Demo: A/B test variations
            logger.info("🧪 A/B TESTING DEMO:")
            ab_test = await ab_test_content_variations.ainvoke({
                "template_name": "social_media_promo",
                "topic": "Bitcoin ATM Security",
                "key_points": ["Bank-level security", "Encrypted transactions", "Verified locations"],
                "variation_count": 3,
                "target_audience": "Security-conscious users"
            })
            
            if ab_test and "variations" in ab_test:
                logger.info(f"🧪 Test ID: {ab_test['test_id']}")
                logger.info(f"📊 Variations: {ab_test['total_variations']}")
                
                for i, variation in enumerate(ab_test["variations"], 1):
                    logger.info(f"   Variation {i}: {variation['variation_focus']}")
                    logger.info(f"   Model: {variation['model_used']} | Length: {variation['character_count']} chars")
            
            logger.info("\n✅ Content generation demo completed successfully!")
            logger.info("\n📝 PRODUCTION NOTES:")
            logger.info("   • Gemini 2.5 Flash integrated for primary content generation")
            logger.info("   • Claude 3.5 Sonnet available for enhanced variations")
            logger.info("   • Automated compliance checking for all content")
            logger.info("   • Multi-platform templates with platform-specific optimization")
            logger.info("   • A/B testing capabilities for performance optimization")
            
            return True
        else:
            logger.warning("⚠️ Demo completed but campaign creation failed")
            return False
            
    except Exception as e:
        logger.error(f"❌ Content generation demo failed: {e}")
        return False


async def main():
    """Run all content generation tests."""
    logger.info("🧪 Starting Content Generation Tests")
    logger.info("=" * 60)
    
    tests = [
        ("Template Listing", test_template_listing),
        ("Single Content Generation", test_single_content_generation),
        ("Content Campaign Creation", test_content_campaign_creation),
        ("A/B Testing Variations", test_ab_testing_variations),
        ("Platform-Specific Content", test_platform_specific_content),
        ("Model Comparison", test_model_comparison),
        ("Compliance Checking", test_compliance_checking),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        logger.info(f"\n🧪 Running {test_name} test...")
        try:
            if await test_func():
                passed += 1
                logger.info(f"✅ {test_name} test passed")
            else:
                logger.error(f"❌ {test_name} test failed")
        except Exception as e:
            logger.error(f"❌ {test_name} test error: {e}")
    
    logger.info(f"\n📊 TEST RESULTS: {passed}/{total} tests passed")
    
    if passed == total:
        logger.info("🎉 All tests passed! Running content generation demo...")
        await run_content_generation_demo()
        logger.info("\n✅ Content generation system is ready for production!")
    else:
        logger.error("❌ Some tests failed. Please fix issues before deployment.")
    
    return passed == total


if __name__ == "__main__":
    asyncio.run(main())
