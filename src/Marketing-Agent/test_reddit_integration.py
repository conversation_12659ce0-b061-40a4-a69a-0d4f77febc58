"""
Test Script for Reddit Integration

This script tests the Reddit API integration for social monitoring
and lead generation capabilities.
"""

import asyncio
import logging
import sys
import os
from datetime import datetime

# Add the project root to the path
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..'))

from src.Marketing_Agent.tools.reddit_integration import (
    monitor_reddit_conversations, 
    analyze_reddit_post_for_engagement,
    RedditAPIClient,
    CRYPTO_SUBREDDITS,
    BITCOIN_ATM_KEYWORDS
)

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


async def test_reddit_api_client():
    """Test the basic Reddit API client functionality."""
    logger.info("Testing Reddit API client...")
    
    try:
        async with RedditAPIClient() as reddit:
            # Test subreddit posts retrieval
            posts = await reddit.get_subreddit_posts("Bitcoin", limit=5)
            
            if posts:
                logger.info(f"✅ Successfully retrieved {len(posts)} posts from r/Bitcoin")
                
                # Show sample post
                sample_post = posts[0]
                logger.info(f"Sample post: '{sample_post.title[:50]}...'")
                logger.info(f"Score: {sample_post.score}, Comments: {sample_post.num_comments}")
                
                # Test comment retrieval for first post
                comments = await reddit.get_post_comments("Bitcoin", sample_post.id, limit=3)
                logger.info(f"✅ Retrieved {len(comments)} comments from post")
                
                return True
            else:
                logger.error("❌ No posts retrieved from Reddit")
                return False
                
    except Exception as e:
        logger.error(f"❌ Reddit API client test failed: {e}")
        return False


async def test_keyword_monitoring():
    """Test keyword-based monitoring functionality."""
    logger.info("Testing Reddit keyword monitoring...")
    
    try:
        # Test with a subset of keywords and subreddits
        test_keywords = ["bitcoin atm", "crypto fees"]
        test_subreddits = ["Bitcoin", "CryptoCurrency"]
        
        result = await monitor_reddit_conversations.ainvoke({
            "keywords": test_keywords,
            "subreddits": test_subreddits,
            "time_filter": "week",
            "max_posts": 20
        })
        
        if result and "total_posts_found" in result:
            logger.info(f"✅ Keyword monitoring successful")
            logger.info(f"Posts found: {result['total_posts_found']}")
            logger.info(f"Engagement opportunities: {result['engagement_opportunities']}")
            
            # Show top opportunities
            if result["top_opportunities"]:
                logger.info("Top engagement opportunities:")
                for i, opp in enumerate(result["top_opportunities"][:3], 1):
                    logger.info(f"{i}. r/{opp['subreddit']}: {opp['title'][:50]}...")
                    logger.info(f"   Score: {opp['opportunity_score']}, Reasons: {opp['opportunity_reasons']}")
            
            return True
        else:
            logger.error(f"❌ Keyword monitoring failed: {result}")
            return False
            
    except Exception as e:
        logger.error(f"❌ Keyword monitoring test failed: {e}")
        return False


async def test_post_analysis():
    """Test individual post analysis functionality."""
    logger.info("Testing Reddit post analysis...")
    
    try:
        # First get a post to analyze
        async with RedditAPIClient() as reddit:
            posts = await reddit.get_subreddit_posts("Bitcoin", limit=5)
            
            if not posts:
                logger.warning("No posts available for analysis test")
                return False
            
            test_post = posts[0]
            
        # Analyze the post
        analysis = await analyze_reddit_post_for_engagement.ainvoke({
            "subreddit": test_post.subreddit,
            "post_id": test_post.id
        })
        
        if analysis and "engagement_potential" in analysis:
            logger.info(f"✅ Post analysis successful")
            logger.info(f"Post: {analysis['title'][:50]}...")
            logger.info(f"Engagement potential: {analysis['engagement_potential']}")
            logger.info(f"Key themes: {analysis['key_themes']}")
            logger.info(f"Response strategy: {analysis['response_strategy']}")
            logger.info(f"Risk level: {analysis['risk_level']}")
            
            return True
        else:
            logger.error(f"❌ Post analysis failed: {analysis}")
            return False
            
    except Exception as e:
        logger.error(f"❌ Post analysis test failed: {e}")
        return False


async def test_subreddit_coverage():
    """Test coverage of cryptocurrency-related subreddits."""
    logger.info("Testing subreddit coverage...")
    
    try:
        successful_subreddits = []
        failed_subreddits = []
        
        async with RedditAPIClient() as reddit:
            for subreddit in CRYPTO_SUBREDDITS[:5]:  # Test first 5
                try:
                    posts = await reddit.get_subreddit_posts(subreddit, limit=1)
                    if posts:
                        successful_subreddits.append(subreddit)
                        logger.info(f"✅ r/{subreddit} accessible")
                    else:
                        failed_subreddits.append(subreddit)
                        logger.warning(f"⚠️ r/{subreddit} returned no posts")
                except Exception as e:
                    failed_subreddits.append(subreddit)
                    logger.error(f"❌ r/{subreddit} failed: {e}")
        
        logger.info(f"Subreddit coverage test complete:")
        logger.info(f"Successful: {len(successful_subreddits)}/{len(CRYPTO_SUBREDDITS[:5])}")
        logger.info(f"Accessible subreddits: {successful_subreddits}")
        
        if failed_subreddits:
            logger.warning(f"Failed subreddits: {failed_subreddits}")
        
        return len(successful_subreddits) > 0
        
    except Exception as e:
        logger.error(f"❌ Subreddit coverage test failed: {e}")
        return False


async def test_keyword_effectiveness():
    """Test effectiveness of Bitcoin ATM keywords."""
    logger.info("Testing keyword effectiveness...")
    
    try:
        keyword_results = {}
        
        async with RedditAPIClient() as reddit:
            for keyword in BITCOIN_ATM_KEYWORDS[:3]:  # Test first 3 keywords
                try:
                    posts = await reddit.search_posts(
                        query=keyword,
                        subreddit="Bitcoin",
                        time_filter="week",
                        limit=5
                    )
                    keyword_results[keyword] = len(posts)
                    logger.info(f"Keyword '{keyword}': {len(posts)} posts found")
                except Exception as e:
                    keyword_results[keyword] = 0
                    logger.error(f"Keyword '{keyword}' failed: {e}")
        
        total_posts = sum(keyword_results.values())
        logger.info(f"Keyword effectiveness test complete:")
        logger.info(f"Total posts found across keywords: {total_posts}")
        logger.info(f"Keyword results: {keyword_results}")
        
        return total_posts > 0
        
    except Exception as e:
        logger.error(f"❌ Keyword effectiveness test failed: {e}")
        return False


async def run_integration_demo():
    """Run a comprehensive demo of Reddit integration capabilities."""
    logger.info("🚀 Running Reddit Integration Demo...")
    logger.info("=" * 60)
    
    try:
        # Simulate a real monitoring session
        demo_result = await monitor_reddit_conversations.ainvoke({
            "keywords": ["bitcoin atm", "coinme", "crypto fees"],
            "subreddits": ["Bitcoin", "CryptoCurrency", "BitcoinBeginners"],
            "time_filter": "day",
            "max_posts": 30
        })
        
        if demo_result and demo_result.get("total_posts_found", 0) > 0:
            logger.info("🎯 REDDIT INTEGRATION DEMO RESULTS:")
            logger.info("=" * 50)
            
            logger.info(f"📊 Total Posts Analyzed: {demo_result['total_posts_found']}")
            logger.info(f"🎯 Engagement Opportunities: {demo_result['engagement_opportunities']}")
            logger.info(f"🔍 Keywords Searched: {', '.join(demo_result['keywords_searched'])}")
            logger.info(f"📱 Subreddits Monitored: {', '.join(demo_result['subreddits_monitored'])}")
            
            if demo_result["top_opportunities"]:
                logger.info("\n🎯 TOP ENGAGEMENT OPPORTUNITIES:")
                for i, opp in enumerate(demo_result["top_opportunities"][:5], 1):
                    logger.info(f"{i}. r/{opp['subreddit']}")
                    logger.info(f"   Title: {opp['title'][:60]}...")
                    logger.info(f"   Score: {opp['opportunity_score']}/10")
                    logger.info(f"   Reasons: {', '.join(opp['opportunity_reasons'])}")
                    logger.info(f"   Engagement: {opp['score']} upvotes, {opp['num_comments']} comments")
                    logger.info("")
            
            logger.info("✅ Reddit integration demo completed successfully!")
            return True
        else:
            logger.warning("⚠️ Demo completed but no opportunities found")
            return False
            
    except Exception as e:
        logger.error(f"❌ Reddit integration demo failed: {e}")
        return False


async def main():
    """Run all Reddit integration tests."""
    logger.info("🧪 Starting Reddit Integration Tests")
    logger.info("=" * 60)
    
    tests = [
        ("Reddit API Client", test_reddit_api_client),
        ("Keyword Monitoring", test_keyword_monitoring),
        ("Post Analysis", test_post_analysis),
        ("Subreddit Coverage", test_subreddit_coverage),
        ("Keyword Effectiveness", test_keyword_effectiveness),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        logger.info(f"\n🧪 Running {test_name} test...")
        try:
            if await test_func():
                passed += 1
                logger.info(f"✅ {test_name} test passed")
            else:
                logger.error(f"❌ {test_name} test failed")
        except Exception as e:
            logger.error(f"❌ {test_name} test error: {e}")
    
    logger.info(f"\n📊 TEST RESULTS: {passed}/{total} tests passed")
    
    if passed == total:
        logger.info("🎉 All tests passed! Running integration demo...")
        await run_integration_demo()
        logger.info("\n✅ Reddit integration is ready for production!")
    else:
        logger.error("❌ Some tests failed. Please fix issues before deployment.")
    
    return passed == total


if __name__ == "__main__":
    asyncio.run(main())
