"""
Test Script for Veo 3 Video Generation Integration

This script tests the Veo 3 AI video generation capabilities for
Bitcoin ATM marketing content creation.
"""

import asyncio
import logging
import sys
import os
from datetime import datetime

# Add the project root to the path
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..'))

from src.Marketing_Agent.tools.veo3_video_generation import (
    generate_bitcoin_atm_video,
    create_video_campaign,
    list_video_templates,
    estimate_video_costs,
    BITCOIN_ATM_VIDEO_TEMPLATES
)

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


async def test_template_listing():
    """Test listing of available video templates."""
    logger.info("Testing video template listing...")
    
    try:
        result = await list_video_templates.ainvoke({})
        
        if result and "templates" in result:
            logger.info(f"✅ Template listing successful")
            logger.info(f"Total templates: {result['total_templates']}")
            logger.info(f"Cost per second: ${result['cost_per_second']}")
            
            # Show sample templates
            logger.info("Available templates:")
            for name, template in list(result["templates"].items())[:3]:
                logger.info(f"  - {name}: {template['duration']}s, ${template['cost_estimate']:.2f}")
            
            return True
        else:
            logger.error(f"❌ Template listing failed: {result}")
            return False
            
    except Exception as e:
        logger.error(f"❌ Template listing test failed: {e}")
        return False


async def test_cost_estimation():
    """Test video cost estimation functionality."""
    logger.info("Testing video cost estimation...")
    
    try:
        test_templates = ["location_promo", "educational_tutorial", "safety_security"]
        
        result = await estimate_video_costs.ainvoke({
            "template_names": test_templates,
            "quantity_per_template": 2
        })
        
        if result and "total_cost_estimate" in result:
            logger.info(f"✅ Cost estimation successful")
            logger.info(f"Total videos: {result['total_videos']}")
            logger.info(f"Total duration: {result['total_duration_seconds']}s")
            logger.info(f"Total cost: ${result['total_cost_estimate']}")
            
            # Show breakdown
            logger.info("Cost breakdown:")
            for template, details in result["cost_breakdown"].items():
                logger.info(f"  {template}: {details['quantity']} videos, ${details['total_cost']:.2f}")
            
            return True
        else:
            logger.error(f"❌ Cost estimation failed: {result}")
            return False
            
    except Exception as e:
        logger.error(f"❌ Cost estimation test failed: {e}")
        return False


async def test_single_video_generation():
    """Test generation of a single video."""
    logger.info("Testing single video generation...")
    
    try:
        result = await generate_bitcoin_atm_video.ainvoke({
            "template_name": "location_promo",
            "location_name": "Downtown Denver Coinme ATM",
            "custom_features": ["24/7 access", "security cameras", "well-lit area"]
        })
        
        if result and result.get("success"):
            logger.info(f"✅ Video generation successful")
            logger.info(f"Video ID: {result['video_id']}")
            logger.info(f"Title: {result['title']}")
            logger.info(f"Duration: {result['duration']}s")
            logger.info(f"Cost: ${result['cost_estimate']:.2f}")
            logger.info(f"Generation time: {result['generation_time']:.2f}s")
            logger.info(f"File URL: {result['file_url']}")
            
            return True
        else:
            logger.error(f"❌ Video generation failed: {result}")
            return False
            
    except Exception as e:
        logger.error(f"❌ Video generation test failed: {e}")
        return False


async def test_video_campaign_creation():
    """Test creation of a multi-video campaign."""
    logger.info("Testing video campaign creation...")
    
    try:
        campaign_templates = ["location_promo", "educational_tutorial", "customer_testimonial"]
        campaign_locations = ["Denver", "Seattle", "Austin"]
        
        result = await create_video_campaign.ainvoke({
            "campaign_name": "Q1 2024 Bitcoin ATM Campaign",
            "template_names": campaign_templates,
            "locations": campaign_locations
        })
        
        if result and "campaign_id" in result:
            logger.info(f"✅ Campaign creation successful")
            logger.info(f"Campaign ID: {result['campaign_id']}")
            logger.info(f"Campaign name: {result['campaign_name']}")
            logger.info(f"Total videos: {result['total_videos']}")
            logger.info(f"Successful generations: {result['successful_generations']}")
            logger.info(f"Total cost: ${result['total_cost_estimate']:.2f}")
            
            # Show generated videos
            logger.info("Generated videos:")
            for i, video in enumerate(result["videos"][:3], 1):
                if video.get("success"):
                    logger.info(f"  {i}. {video['title']} - ${video['cost_estimate']:.2f}")
            
            return True
        else:
            logger.error(f"❌ Campaign creation failed: {result}")
            return False
            
    except Exception as e:
        logger.error(f"❌ Campaign creation test failed: {e}")
        return False


async def test_custom_prompt_generation():
    """Test video generation with custom prompts."""
    logger.info("Testing custom prompt video generation...")
    
    try:
        custom_prompt = """Create a dynamic 30-second video showcasing a Bitcoin ATM in a busy coffee shop. 
        Show diverse customers casually using the machine while getting their morning coffee. 
        Emphasize the convenience and integration into daily life. 
        End with the Coinme logo and 'Bitcoin as easy as your morning coffee' tagline."""
        
        result = await generate_bitcoin_atm_video.ainvoke({
            "template_name": "brand_awareness",
            "location_name": "Starbucks Downtown",
            "custom_prompt": custom_prompt
        })
        
        if result and result.get("success"):
            logger.info(f"✅ Custom prompt generation successful")
            logger.info(f"Video ID: {result['video_id']}")
            logger.info(f"Custom prompt used: {len(result['prompt_used'])} characters")
            logger.info(f"Duration: {result['duration']}s")
            logger.info(f"Cost: ${result['cost_estimate']:.2f}")
            
            return True
        else:
            logger.error(f"❌ Custom prompt generation failed: {result}")
            return False
            
    except Exception as e:
        logger.error(f"❌ Custom prompt generation test failed: {e}")
        return False


async def test_template_validation():
    """Test template validation and error handling."""
    logger.info("Testing template validation...")
    
    try:
        # Test with invalid template
        result = await generate_bitcoin_atm_video.ainvoke({
            "template_name": "invalid_template_name"
        })
        
        if result and "error" in result and "available_templates" in result:
            logger.info(f"✅ Template validation successful")
            logger.info(f"Error message: {result['error']}")
            logger.info(f"Available templates provided: {len(result['available_templates'])}")
            
            return True
        else:
            logger.error(f"❌ Template validation failed: {result}")
            return False
            
    except Exception as e:
        logger.error(f"❌ Template validation test failed: {e}")
        return False


async def test_template_variety():
    """Test different types of video templates."""
    logger.info("Testing template variety...")
    
    try:
        template_types = {
            "promotional": "location_promo",
            "educational": "educational_tutorial", 
            "testimonial": "customer_testimonial",
            "brand": "brand_awareness"
        }
        
        successful_types = 0
        
        for video_type, template_name in template_types.items():
            result = await generate_bitcoin_atm_video.ainvoke({
                "template_name": template_name,
                "location_name": f"Test {video_type.title()} Location"
            })
            
            if result and result.get("success"):
                successful_types += 1
                logger.info(f"✅ {video_type} template successful: {result['title']}")
            else:
                logger.error(f"❌ {video_type} template failed")
        
        logger.info(f"Template variety test: {successful_types}/{len(template_types)} types successful")
        return successful_types == len(template_types)
        
    except Exception as e:
        logger.error(f"❌ Template variety test failed: {e}")
        return False


async def run_veo3_integration_demo():
    """Run a comprehensive demo of Veo 3 integration capabilities."""
    logger.info("🚀 Running Veo 3 Integration Demo...")
    logger.info("=" * 60)
    
    try:
        # Demo: Create a complete marketing campaign
        logger.info("🎬 CREATING COMPLETE MARKETING CAMPAIGN")
        logger.info("=" * 50)
        
        campaign_result = await create_video_campaign.ainvoke({
            "campaign_name": "Coinme Q1 2024 Marketing Blitz",
            "template_names": [
                "location_promo",
                "educational_tutorial", 
                "safety_security",
                "customer_testimonial",
                "brand_awareness"
            ],
            "locations": [
                "Times Square NYC",
                "Union Station Denver", 
                "Pike Place Market Seattle",
                "South by Southwest Austin",
                "Hollywood Boulevard LA"
            ]
        })
        
        if campaign_result and "campaign_id" in campaign_result:
            logger.info("🎯 CAMPAIGN DEMO RESULTS:")
            logger.info("=" * 40)
            
            logger.info(f"📊 Campaign ID: {campaign_result['campaign_id']}")
            logger.info(f"🎬 Total Videos: {campaign_result['total_videos']}")
            logger.info(f"✅ Successful: {campaign_result['successful_generations']}")
            logger.info(f"💰 Total Cost: ${campaign_result['total_cost_estimate']:.2f}")
            logger.info(f"📍 Locations: {', '.join(campaign_result['locations_covered'])}")
            
            logger.info("\n🎬 GENERATED VIDEOS:")
            for i, video in enumerate(campaign_result["videos"], 1):
                if video.get("success"):
                    logger.info(f"{i}. {video['title']}")
                    logger.info(f"   Duration: {video['duration']}s | Cost: ${video['cost_estimate']:.2f}")
                    logger.info(f"   File: {video['file_url']}")
                    logger.info("")
            
            # Demo: Cost analysis
            logger.info("💰 COST ANALYSIS:")
            cost_analysis = await estimate_video_costs.ainvoke({
                "template_names": campaign_result["templates_used"],
                "quantity_per_template": 3  # Scale up for multiple locations
            })
            
            if cost_analysis:
                logger.info(f"📊 Scaled Campaign (3x): ${cost_analysis['total_cost_estimate']:.2f}")
                logger.info(f"⏱️  Total Duration: {cost_analysis['total_duration_seconds']}s")
                logger.info(f"🎬 Total Videos: {cost_analysis['total_videos']}")
            
            logger.info("\n✅ Veo 3 integration demo completed successfully!")
            logger.info("\n📝 PRODUCTION NOTES:")
            logger.info("   • Actual Veo 3 API integration required for live generation")
            logger.info("   • Videos generated at $0.50/second on Vertex AI")
            logger.info("   • Estimated processing time: 2-5 minutes per video")
            logger.info("   • Output formats: MP4, WebM with various resolutions")
            
            return True
        else:
            logger.warning("⚠️ Demo completed but campaign creation failed")
            return False
            
    except Exception as e:
        logger.error(f"❌ Veo 3 integration demo failed: {e}")
        return False


async def main():
    """Run all Veo 3 integration tests."""
    logger.info("🧪 Starting Veo 3 Integration Tests")
    logger.info("=" * 60)
    
    tests = [
        ("Template Listing", test_template_listing),
        ("Cost Estimation", test_cost_estimation),
        ("Single Video Generation", test_single_video_generation),
        ("Video Campaign Creation", test_video_campaign_creation),
        ("Custom Prompt Generation", test_custom_prompt_generation),
        ("Template Validation", test_template_validation),
        ("Template Variety", test_template_variety),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        logger.info(f"\n🧪 Running {test_name} test...")
        try:
            if await test_func():
                passed += 1
                logger.info(f"✅ {test_name} test passed")
            else:
                logger.error(f"❌ {test_name} test failed")
        except Exception as e:
            logger.error(f"❌ {test_name} test error: {e}")
    
    logger.info(f"\n📊 TEST RESULTS: {passed}/{total} tests passed")
    
    if passed == total:
        logger.info("🎉 All tests passed! Running integration demo...")
        await run_veo3_integration_demo()
        logger.info("\n✅ Veo 3 integration is ready for production!")
    else:
        logger.error("❌ Some tests failed. Please fix issues before deployment.")
    
    return passed == total


if __name__ == "__main__":
    asyncio.run(main())
