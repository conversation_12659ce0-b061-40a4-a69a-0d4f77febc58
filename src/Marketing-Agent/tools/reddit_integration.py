"""
Reddit API Integration for Marketing Agent

This module provides comprehensive Reddit API integration for social monitoring,
lead generation, and engagement opportunities in cryptocurrency communities.
"""

import logging
import asyncio
import aiohttp
import json
from typing import List, Dict, Any, Optional
from datetime import datetime, timedelta
from dataclasses import dataclass
from langchain_core.tools import tool

logger = logging.getLogger(__name__)


@dataclass
class RedditPost:
    """Represents a Reddit post with relevant metadata."""
    id: str
    title: str
    content: str
    author: str
    subreddit: str
    score: int
    num_comments: int
    created_utc: float
    url: str
    permalink: str
    is_self: bool
    upvote_ratio: float
    flair_text: Optional[str] = None


@dataclass
class RedditComment:
    """Represents a Reddit comment with relevant metadata."""
    id: str
    body: str
    author: str
    score: int
    created_utc: float
    parent_id: str
    post_id: str
    subreddit: str
    permalink: str


class RedditAPIClient:
    """
    Reddit API client for social monitoring and engagement.
    
    Uses Reddit's public API endpoints that don't require OAuth for read-only access.
    For posting/commenting, OAuth2 would be required.
    """
    
    def __init__(self):
        self.base_url = "https://www.reddit.com"
        self.session = None
        self.user_agent = "TKC_Marketing_Agent/1.0"
        
    async def __aenter__(self):
        """Async context manager entry."""
        self.session = aiohttp.ClientSession(
            headers={"User-Agent": self.user_agent},
            timeout=aiohttp.ClientTimeout(total=30)
        )
        return self
        
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit."""
        if self.session:
            await self.session.close()
    
    async def search_posts(
        self, 
        query: str, 
        subreddit: Optional[str] = None,
        sort: str = "relevance",
        time_filter: str = "week",
        limit: int = 25
    ) -> List[RedditPost]:
        """
        Search for posts containing specific keywords.
        
        Args:
            query: Search query string
            subreddit: Specific subreddit to search (optional)
            sort: Sort method (relevance, hot, top, new)
            time_filter: Time filter (hour, day, week, month, year, all)
            limit: Maximum number of posts to return
        """
        try:
            if subreddit:
                url = f"{self.base_url}/r/{subreddit}/search.json"
            else:
                url = f"{self.base_url}/search.json"
            
            params = {
                "q": query,
                "sort": sort,
                "t": time_filter,
                "limit": limit,
                "restrict_sr": "true" if subreddit else "false"
            }
            
            async with self.session.get(url, params=params) as response:
                if response.status == 200:
                    data = await response.json()
                    posts = []
                    
                    for post_data in data.get("data", {}).get("children", []):
                        post = post_data.get("data", {})
                        posts.append(RedditPost(
                            id=post.get("id"),
                            title=post.get("title", ""),
                            content=post.get("selftext", ""),
                            author=post.get("author", ""),
                            subreddit=post.get("subreddit", ""),
                            score=post.get("score", 0),
                            num_comments=post.get("num_comments", 0),
                            created_utc=post.get("created_utc", 0),
                            url=post.get("url", ""),
                            permalink=f"{self.base_url}{post.get('permalink', '')}",
                            is_self=post.get("is_self", False),
                            upvote_ratio=post.get("upvote_ratio", 0.0),
                            flair_text=post.get("link_flair_text")
                        ))
                    
                    logger.info(f"Found {len(posts)} posts for query: {query}")
                    return posts
                else:
                    logger.error(f"Reddit API error: {response.status}")
                    return []
                    
        except Exception as e:
            logger.error(f"Error searching Reddit posts: {e}")
            return []
    
    async def get_subreddit_posts(
        self,
        subreddit: str,
        sort: str = "hot",
        time_filter: str = "day",
        limit: int = 25
    ) -> List[RedditPost]:
        """
        Get posts from a specific subreddit.
        
        Args:
            subreddit: Subreddit name (without r/)
            sort: Sort method (hot, new, top, rising)
            time_filter: Time filter for 'top' sort
            limit: Maximum number of posts to return
        """
        try:
            if sort == "top":
                url = f"{self.base_url}/r/{subreddit}/top.json"
                params = {"t": time_filter, "limit": limit}
            else:
                url = f"{self.base_url}/r/{subreddit}/{sort}.json"
                params = {"limit": limit}
            
            async with self.session.get(url, params=params) as response:
                if response.status == 200:
                    data = await response.json()
                    posts = []
                    
                    for post_data in data.get("data", {}).get("children", []):
                        post = post_data.get("data", {})
                        posts.append(RedditPost(
                            id=post.get("id"),
                            title=post.get("title", ""),
                            content=post.get("selftext", ""),
                            author=post.get("author", ""),
                            subreddit=post.get("subreddit", ""),
                            score=post.get("score", 0),
                            num_comments=post.get("num_comments", 0),
                            created_utc=post.get("created_utc", 0),
                            url=post.get("url", ""),
                            permalink=f"{self.base_url}{post.get('permalink', '')}",
                            is_self=post.get("is_self", False),
                            upvote_ratio=post.get("upvote_ratio", 0.0),
                            flair_text=post.get("link_flair_text")
                        ))
                    
                    logger.info(f"Retrieved {len(posts)} posts from r/{subreddit}")
                    return posts
                else:
                    logger.error(f"Reddit API error: {response.status}")
                    return []
                    
        except Exception as e:
            logger.error(f"Error getting subreddit posts: {e}")
            return []
    
    async def get_post_comments(
        self,
        subreddit: str,
        post_id: str,
        sort: str = "best",
        limit: int = 50
    ) -> List[RedditComment]:
        """
        Get comments from a specific post.
        
        Args:
            subreddit: Subreddit name
            post_id: Post ID
            sort: Comment sort method (best, top, new, controversial)
            limit: Maximum number of comments to return
        """
        try:
            url = f"{self.base_url}/r/{subreddit}/comments/{post_id}.json"
            params = {"sort": sort, "limit": limit}
            
            async with self.session.get(url, params=params) as response:
                if response.status == 200:
                    data = await response.json()
                    comments = []
                    
                    # Reddit returns post data and comments in separate sections
                    if len(data) > 1:
                        comment_data = data[1].get("data", {}).get("children", [])
                        
                        for comment_item in comment_data:
                            comment = comment_item.get("data", {})
                            if comment.get("body") and comment.get("body") != "[deleted]":
                                comments.append(RedditComment(
                                    id=comment.get("id"),
                                    body=comment.get("body", ""),
                                    author=comment.get("author", ""),
                                    score=comment.get("score", 0),
                                    created_utc=comment.get("created_utc", 0),
                                    parent_id=comment.get("parent_id", ""),
                                    post_id=post_id,
                                    subreddit=subreddit,
                                    permalink=f"{self.base_url}{comment.get('permalink', '')}"
                                ))
                    
                    logger.info(f"Retrieved {len(comments)} comments from post {post_id}")
                    return comments
                else:
                    logger.error(f"Reddit API error: {response.status}")
                    return []
                    
        except Exception as e:
            logger.error(f"Error getting post comments: {e}")
            return []


# Cryptocurrency-related subreddits for monitoring
CRYPTO_SUBREDDITS = [
    "Bitcoin",
    "CryptoCurrency", 
    "BitcoinBeginners",
    "btc",
    "CryptoMarkets",
    "BitcoinATM",
    "CoinBase",
    "CashApp",
    "PersonalFinance",
    "investing",
    "Scams",
    "legaladvice"
]

# Keywords for Bitcoin ATM related discussions
BITCOIN_ATM_KEYWORDS = [
    "bitcoin atm",
    "crypto atm", 
    "coinme",
    "bitcoin machine",
    "buy bitcoin cash",
    "bitcoin fees",
    "crypto fees",
    "bitcoin scam",
    "coinstar bitcoin",
    "bitcoin depot",
    "where to buy bitcoin",
    "bitcoin verification",
    "bitcoin id required"
]


@tool
async def monitor_reddit_conversations(
    keywords: List[str] = None,
    subreddits: List[str] = None,
    time_filter: str = "day",
    max_posts: int = 50
) -> Dict[str, Any]:
    """
    Monitor Reddit conversations for Bitcoin ATM related discussions.
    
    Args:
        keywords: List of keywords to search for
        subreddits: List of subreddits to monitor
        time_filter: Time filter (hour, day, week)
        max_posts: Maximum posts to analyze
    
    Returns:
        Dictionary with found conversations and analysis
    """
    try:
        if keywords is None:
            keywords = BITCOIN_ATM_KEYWORDS[:5]  # Use top 5 keywords
        
        if subreddits is None:
            subreddits = CRYPTO_SUBREDDITS[:3]  # Use top 3 subreddits
        
        all_posts = []
        
        async with RedditAPIClient() as reddit:
            # Search for keyword-based posts
            for keyword in keywords:
                for subreddit in subreddits:
                    posts = await reddit.search_posts(
                        query=keyword,
                        subreddit=subreddit,
                        time_filter=time_filter,
                        limit=max_posts // len(keywords) // len(subreddits)
                    )
                    all_posts.extend(posts)
        
        # Remove duplicates based on post ID
        unique_posts = {post.id: post for post in all_posts}.values()
        
        # Analyze posts for engagement opportunities
        opportunities = []
        for post in unique_posts:
            opportunity_score = 0
            opportunity_reasons = []
            
            # Score based on content analysis
            content_lower = (post.title + " " + post.content).lower()
            
            if any(word in content_lower for word in ["fee", "expensive", "cost"]):
                opportunity_score += 3
                opportunity_reasons.append("fee_concern")
            
            if any(word in content_lower for word in ["scam", "fraud", "fake"]):
                opportunity_score += 5
                opportunity_reasons.append("scam_concern")
            
            if any(word in content_lower for word in ["help", "question", "how"]):
                opportunity_score += 2
                opportunity_reasons.append("help_request")
            
            if any(word in content_lower for word in ["coinme", "bitcoin depot", "coinstar"]):
                opportunity_score += 4
                opportunity_reasons.append("competitor_mention")
            
            # Score based on engagement metrics
            if post.score < 5 and post.num_comments < 10:
                opportunity_score += 1  # Less competition for engagement
            
            if opportunity_score >= 3:
                opportunities.append({
                    "post_id": post.id,
                    "title": post.title,
                    "subreddit": post.subreddit,
                    "author": post.author,
                    "score": post.score,
                    "num_comments": post.num_comments,
                    "opportunity_score": opportunity_score,
                    "opportunity_reasons": opportunity_reasons,
                    "permalink": post.permalink,
                    "created_utc": post.created_utc
                })
        
        # Sort opportunities by score
        opportunities.sort(key=lambda x: x["opportunity_score"], reverse=True)
        
        result = {
            "total_posts_found": len(unique_posts),
            "engagement_opportunities": len(opportunities),
            "top_opportunities": opportunities[:10],  # Top 10 opportunities
            "keywords_searched": keywords,
            "subreddits_monitored": subreddits,
            "time_filter": time_filter,
            "timestamp": datetime.utcnow().isoformat()
        }
        
        logger.info(f"Reddit monitoring complete: {len(opportunities)} opportunities found")
        return result
        
    except Exception as e:
        logger.error(f"Reddit monitoring error: {e}")
        return {"error": str(e), "opportunities": []}


@tool
async def analyze_reddit_post_for_engagement(
    subreddit: str,
    post_id: str
) -> Dict[str, Any]:
    """
    Analyze a specific Reddit post for engagement opportunities.
    
    Args:
        subreddit: Subreddit name
        post_id: Reddit post ID
    
    Returns:
        Analysis of engagement opportunity and recommended approach
    """
    try:
        async with RedditAPIClient() as reddit:
            # Get post details
            posts = await reddit.get_subreddit_posts(subreddit, limit=1)
            target_post = None
            
            # Find the specific post (simplified - in production would use direct API call)
            for post in posts:
                if post.id == post_id:
                    target_post = post
                    break
            
            if not target_post:
                return {"error": "Post not found"}
            
            # Get comments for context
            comments = await reddit.get_post_comments(subreddit, post_id, limit=20)
            
            # Analyze content
            full_content = target_post.title + " " + target_post.content
            content_lower = full_content.lower()
            
            analysis = {
                "post_id": post_id,
                "subreddit": subreddit,
                "title": target_post.title,
                "author": target_post.author,
                "score": target_post.score,
                "num_comments": target_post.num_comments,
                "engagement_potential": "medium",
                "recommended_approach": "helpful_response",
                "key_themes": [],
                "response_strategy": "",
                "risk_level": "low"
            }
            
            # Identify themes and determine approach
            if any(word in content_lower for word in ["fee", "expensive", "cost", "price"]):
                analysis["key_themes"].append("fee_concern")
                analysis["response_strategy"] = "fee_transparency"
                analysis["engagement_potential"] = "high"
            
            if any(word in content_lower for word in ["scam", "fraud", "fake", "steal"]):
                analysis["key_themes"].append("scam_concern")
                analysis["response_strategy"] = "trust_building"
                analysis["engagement_potential"] = "high"
                analysis["risk_level"] = "medium"
            
            if any(word in content_lower for word in ["help", "how", "where", "question"]):
                analysis["key_themes"].append("help_request")
                analysis["response_strategy"] = "educational_response"
                analysis["engagement_potential"] = "high"
            
            if any(word in content_lower for word in ["coinme", "bitcoin depot", "coinstar"]):
                analysis["key_themes"].append("competitor_mention")
                analysis["response_strategy"] = "competitive_differentiation"
                analysis["engagement_potential"] = "medium"
            
            # Analyze existing comments for context
            comment_sentiment = "neutral"
            if comments:
                negative_comments = sum(1 for c in comments if any(word in c.body.lower() for word in ["bad", "terrible", "scam", "avoid"]))
                if negative_comments > len(comments) * 0.3:
                    comment_sentiment = "negative"
                    analysis["risk_level"] = "high"
            
            analysis["comment_sentiment"] = comment_sentiment
            analysis["total_comments_analyzed"] = len(comments)
            
            logger.info(f"Analyzed Reddit post {post_id}: {analysis['engagement_potential']} potential")
            return analysis
            
    except Exception as e:
        logger.error(f"Reddit post analysis error: {e}")
        return {"error": str(e)}
