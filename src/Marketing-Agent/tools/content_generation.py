"""
Multi-Platform Content Generation System for Marketing Agent

This module provides comprehensive content generation capabilities using
Gemini 2.5 Flash and Claude 3.5 Sonnet for various marketing channels.
"""

import logging
import asyncio
import json
from typing import List, Dict, Any, Optional, Union
from datetime import datetime
from dataclasses import dataclass
from enum import Enum
from langchain_core.tools import tool
from langchain_google_vertexai import ChatVertexAI

logger = logging.getLogger(__name__)


class ContentType(Enum):
    """Types of content that can be generated."""
    BLOG_POST = "blog_post"
    SOCIAL_MEDIA = "social_media"
    EMAIL_CAMPAIGN = "email_campaign"
    RESPONSE_DRAFT = "response_draft"
    PRESS_RELEASE = "press_release"
    LANDING_PAGE = "landing_page"
    AD_COPY = "ad_copy"
    VIDEO_SCRIPT = "video_script"


class Platform(Enum):
    """Target platforms for content."""
    REDDIT = "reddit"
    YOUTUBE = "youtube"
    FACEBOOK = "facebook"
    TWITTER = "twitter"
    LINKEDIN = "linkedin"
    INSTAGRAM = "instagram"
    EMAIL = "email"
    WEBSITE = "website"
    BLOG = "blog"


class Tone(Enum):
    """Content tone options."""
    PROFESSIONAL = "professional"
    CASUAL = "casual"
    EDUCATIONAL = "educational"
    PROMOTIONAL = "promotional"
    HELPFUL = "helpful"
    AUTHORITATIVE = "authoritative"
    CONVERSATIONAL = "conversational"


@dataclass
class ContentTemplate:
    """Template for content generation."""
    name: str
    content_type: ContentType
    platform: Platform
    tone: Tone
    max_length: int
    structure: List[str]
    key_elements: List[str]
    call_to_action: str
    compliance_notes: List[str]


@dataclass
class GeneratedContent:
    """Generated content with metadata."""
    id: str
    title: str
    content: str
    content_type: ContentType
    platform: Platform
    tone: Tone
    word_count: int
    character_count: int
    hashtags: List[str]
    key_messages: List[str]
    compliance_status: str
    generated_at: str
    model_used: str


class ContentGenerator:
    """
    Multi-platform content generator using Gemini 2.5 Flash and Claude 3.5 Sonnet.
    """
    
    def __init__(self, project_id: str, location: str = "us-central1"):
        self.project_id = project_id
        self.location = location
        
        # Initialize Gemini 2.5 Flash for primary content generation
        self.gemini_model = ChatVertexAI(
            model_name="gemini-2.5-flash",
            project=project_id,
            location=location,
            temperature=0.7,
            max_output_tokens=2048
        )
        
    async def generate_content(
        self,
        template: ContentTemplate,
        topic: str,
        key_points: List[str],
        target_audience: str,
        custom_instructions: Optional[str] = None,
        use_claude: bool = False
    ) -> GeneratedContent:
        """
        Generate content based on template and requirements.
        
        Args:
            template: Content template to use
            topic: Main topic for the content
            key_points: Key points to include
            target_audience: Target audience description
            custom_instructions: Additional custom instructions
            use_claude: Whether to use Claude 3.5 Sonnet instead of Gemini
        
        Returns:
            GeneratedContent object with generated content and metadata
        """
        try:
            # Build the generation prompt
            prompt = self._build_content_prompt(
                template, topic, key_points, target_audience, custom_instructions
            )
            
            # Generate content using selected model
            if use_claude:
                # In production, this would use Claude 3.5 Sonnet API
                content = await self._generate_with_claude(prompt, template)
                model_used = "claude-3.5-sonnet"
            else:
                content = await self._generate_with_gemini(prompt, template)
                model_used = "gemini-2.5-flash"
            
            # Extract hashtags and key messages
            hashtags = self._extract_hashtags(content)
            key_messages = self._extract_key_messages(content, key_points)
            
            # Check compliance
            compliance_status = self._check_compliance(content, template)
            
            # Create result object
            content_id = f"content_{datetime.utcnow().strftime('%Y%m%d_%H%M%S')}"
            
            result = GeneratedContent(
                id=content_id,
                title=self._extract_title(content),
                content=content,
                content_type=template.content_type,
                platform=template.platform,
                tone=template.tone,
                word_count=len(content.split()),
                character_count=len(content),
                hashtags=hashtags,
                key_messages=key_messages,
                compliance_status=compliance_status,
                generated_at=datetime.utcnow().isoformat(),
                model_used=model_used
            )
            
            logger.info(f"Content generated: {content_id} ({template.content_type.value})")
            return result
            
        except Exception as e:
            logger.error(f"Content generation failed: {e}")
            raise
    
    async def _generate_with_gemini(self, prompt: str, template: ContentTemplate) -> str:
        """Generate content using Gemini 2.5 Flash."""
        try:
            response = await self.gemini_model.ainvoke(prompt)
            return response.content
        except Exception as e:
            logger.error(f"Gemini generation error: {e}")
            # Fallback to mock content
            return self._generate_mock_content(template)
    
    async def _generate_with_claude(self, prompt: str, template: ContentTemplate) -> str:
        """Generate content using Claude 3.5 Sonnet (mock implementation)."""
        try:
            # In production, this would call Claude API
            # For now, simulate with enhanced mock content
            await asyncio.sleep(1)  # Simulate API call
            return self._generate_mock_content(template, enhanced=True)
        except Exception as e:
            logger.error(f"Claude generation error: {e}")
            return self._generate_mock_content(template)
    
    def _build_content_prompt(
        self,
        template: ContentTemplate,
        topic: str,
        key_points: List[str],
        target_audience: str,
        custom_instructions: Optional[str]
    ) -> str:
        """Build the content generation prompt."""
        prompt = f"""
        Create {template.content_type.value} content for {template.platform.value} with the following specifications:

        TOPIC: {topic}
        TARGET AUDIENCE: {target_audience}
        TONE: {template.tone.value}
        MAX LENGTH: {template.max_length} characters
        
        KEY POINTS TO INCLUDE:
        {chr(10).join(f"- {point}" for point in key_points)}
        
        CONTENT STRUCTURE:
        {chr(10).join(f"- {element}" for element in template.structure)}
        
        KEY ELEMENTS TO INCLUDE:
        {chr(10).join(f"- {element}" for element in template.key_elements)}
        
        CALL TO ACTION: {template.call_to_action}
        
        COMPLIANCE REQUIREMENTS:
        {chr(10).join(f"- {note}" for note in template.compliance_notes)}
        """
        
        if custom_instructions:
            prompt += f"\n\nADDITIONAL INSTRUCTIONS:\n{custom_instructions}"
        
        prompt += f"""
        
        Please generate engaging, compliant content that:
        1. Stays within the character limit
        2. Matches the specified tone and platform
        3. Includes all key points naturally
        4. Follows the content structure
        5. Ends with the specified call to action
        6. Complies with all requirements
        """
        
        return prompt
    
    def _generate_mock_content(self, template: ContentTemplate, enhanced: bool = False) -> str:
        """Generate mock content for testing."""
        content_samples = {
            ContentType.BLOG_POST: {
                "title": "Understanding Bitcoin ATMs: Your Gateway to Cryptocurrency",
                "content": """Bitcoin ATMs are revolutionizing how people access cryptocurrency. These machines provide a simple, secure way to buy Bitcoin with cash, making digital currency accessible to everyone.

What makes Bitcoin ATMs special? They offer instant transactions, require minimal technical knowledge, and are available 24/7 in convenient locations. Whether you're a crypto newcomer or experienced trader, Bitcoin ATMs provide unmatched convenience.

At Coinme, we've made the process even simpler. Our ATMs feature intuitive interfaces, competitive rates, and industry-leading security. With thousands of locations nationwide, there's likely a Coinme Bitcoin ATM near you.

Ready to experience the future of money? Find your nearest Coinme Bitcoin ATM today and join the cryptocurrency revolution."""
            },
            ContentType.SOCIAL_MEDIA: {
                "content": "🚀 New to Bitcoin? Our ATMs make it simple! Just insert cash, scan your wallet, and you're done. No complicated exchanges or lengthy verification processes. Find your nearest location at coinme.com #Bitcoin #Cryptocurrency #FinTech #Innovation"
            },
            ContentType.EMAIL_CAMPAIGN: {
                "subject": "Your Local Bitcoin ATM is Ready When You Are",
                "content": """Hi there!

Did you know there's probably a Bitcoin ATM within 10 minutes of where you are right now? 

Our Coinme Bitcoin ATMs make buying cryptocurrency as easy as getting cash from a regular ATM. No apps to download, no accounts to create, no waiting for verification.

Here's how simple it is:
1. Find a location near you
2. Insert your cash
3. Scan your Bitcoin wallet
4. Done!

Ready to try it? Use our location finder to discover your nearest Coinme Bitcoin ATM.

Best regards,
The Coinme Team"""
            },
            ContentType.RESPONSE_DRAFT: {
                "content": "Thanks for your question about Bitcoin ATM fees! Our pricing is transparent and competitive. While there is a service fee for the convenience and security we provide, many customers find the instant access and ease of use worth it. You can view our current rates at coinme.com/fees. Happy to answer any other questions!"
            }
        }
        
        base_content = content_samples.get(template.content_type, {"content": "Sample content for " + template.content_type.value})
        
        if enhanced:
            # Simulate Claude's enhanced output
            content = base_content.get("content", "")
            content += "\n\n[Enhanced with Claude 3.5 Sonnet - more nuanced language and structure]"
            return content
        
        return base_content.get("content", "")
    
    def _extract_title(self, content: str) -> str:
        """Extract title from content."""
        lines = content.split('\n')
        for line in lines:
            if line.strip() and not line.startswith('#'):
                return line.strip()[:100]
        return "Generated Content"
    
    def _extract_hashtags(self, content: str) -> List[str]:
        """Extract hashtags from content."""
        import re
        hashtags = re.findall(r'#\w+', content)
        return list(set(hashtags))
    
    def _extract_key_messages(self, content: str, key_points: List[str]) -> List[str]:
        """Extract key messages that were successfully included."""
        messages = []
        content_lower = content.lower()
        
        for point in key_points:
            if any(word.lower() in content_lower for word in point.split()):
                messages.append(point)
        
        return messages
    
    def _check_compliance(self, content: str, template: ContentTemplate) -> str:
        """Check content compliance."""
        issues = []
        
        # Check length
        if len(content) > template.max_length:
            issues.append("exceeds_length_limit")
        
        # Check for compliance keywords
        content_lower = content.lower()
        
        # Financial compliance checks
        if any(word in content_lower for word in ["guaranteed", "risk-free", "investment advice"]):
            issues.append("financial_compliance_risk")
        
        # Platform-specific checks
        if template.platform == Platform.REDDIT and len(content) > 10000:
            issues.append("reddit_length_limit")
        
        if template.platform == Platform.TWITTER and len(content) > 280:
            issues.append("twitter_length_limit")
        
        return "compliant" if not issues else f"issues: {', '.join(issues)}"


# Predefined content templates for Bitcoin ATM marketing
CONTENT_TEMPLATES = {
    "reddit_educational_post": ContentTemplate(
        name="Reddit Educational Post",
        content_type=ContentType.SOCIAL_MEDIA,
        platform=Platform.REDDIT,
        tone=Tone.EDUCATIONAL,
        max_length=10000,
        structure=["Introduction", "Main explanation", "Benefits", "Call to action"],
        key_elements=["Clear explanation", "Helpful tone", "No direct promotion"],
        call_to_action="Feel free to ask questions in the comments!",
        compliance_notes=["No financial advice", "Educational only", "Transparent about affiliation"]
    ),
    
    "youtube_comment_response": ContentTemplate(
        name="YouTube Comment Response",
        content_type=ContentType.RESPONSE_DRAFT,
        platform=Platform.YOUTUBE,
        tone=Tone.HELPFUL,
        max_length=500,
        structure=["Acknowledgment", "Helpful information", "Soft CTA"],
        key_elements=["Address concern", "Provide value", "Build trust"],
        call_to_action="Check out our website for more info",
        compliance_notes=["No spam", "Genuinely helpful", "Transparent"]
    ),
    
    "blog_post_educational": ContentTemplate(
        name="Educational Blog Post",
        content_type=ContentType.BLOG_POST,
        platform=Platform.BLOG,
        tone=Tone.EDUCATIONAL,
        max_length=2000,
        structure=["Headline", "Introduction", "Main content", "Benefits", "Conclusion", "CTA"],
        key_elements=["SEO optimization", "Clear structure", "Actionable insights"],
        call_to_action="Find your nearest Coinme Bitcoin ATM",
        compliance_notes=["Educational content", "No financial advice", "Factual accuracy"]
    ),
    
    "email_newsletter": ContentTemplate(
        name="Email Newsletter",
        content_type=ContentType.EMAIL_CAMPAIGN,
        platform=Platform.EMAIL,
        tone=Tone.CONVERSATIONAL,
        max_length=1500,
        structure=["Subject line", "Greeting", "Main content", "Benefits", "CTA"],
        key_elements=["Personal tone", "Value proposition", "Clear CTA"],
        call_to_action="Visit your nearest location today",
        compliance_notes=["CAN-SPAM compliance", "Unsubscribe option", "Clear sender"]
    ),
    
    "social_media_promo": ContentTemplate(
        name="Social Media Promotion",
        content_type=ContentType.SOCIAL_MEDIA,
        platform=Platform.FACEBOOK,
        tone=Tone.PROMOTIONAL,
        max_length=500,
        structure=["Hook", "Value proposition", "CTA"],
        key_elements=["Eye-catching", "Clear benefit", "Strong CTA"],
        call_to_action="Find locations near you",
        compliance_notes=["Platform guidelines", "No misleading claims", "Clear disclosure"]
    )
}


@tool
async def generate_marketing_content(
    template_name: str,
    topic: str,
    key_points: List[str],
    target_audience: str = "General cryptocurrency users",
    custom_instructions: Optional[str] = None,
    use_claude: bool = False,
    project_id: str = "tkc-v5-production"
) -> Dict[str, Any]:
    """
    Generate marketing content using specified template and AI model.
    
    Args:
        template_name: Name of the content template to use
        topic: Main topic for the content
        key_points: Key points to include in the content
        target_audience: Description of target audience
        custom_instructions: Additional custom instructions
        use_claude: Whether to use Claude 3.5 Sonnet instead of Gemini
        project_id: GCP project ID
    
    Returns:
        Dictionary with generated content and metadata
    """
    try:
        # Get the template
        if template_name not in CONTENT_TEMPLATES:
            available_templates = list(CONTENT_TEMPLATES.keys())
            return {
                "error": f"Template '{template_name}' not found",
                "available_templates": available_templates
            }
        
        template = CONTENT_TEMPLATES[template_name]
        
        # Initialize generator
        generator = ContentGenerator(project_id)
        
        # Generate content
        result = await generator.generate_content(
            template=template,
            topic=topic,
            key_points=key_points,
            target_audience=target_audience,
            custom_instructions=custom_instructions,
            use_claude=use_claude
        )
        
        # Return structured result
        return {
            "success": True,
            "content_id": result.id,
            "title": result.title,
            "content": result.content,
            "content_type": result.content_type.value,
            "platform": result.platform.value,
            "tone": result.tone.value,
            "word_count": result.word_count,
            "character_count": result.character_count,
            "hashtags": result.hashtags,
            "key_messages_included": result.key_messages,
            "compliance_status": result.compliance_status,
            "model_used": result.model_used,
            "generated_at": result.generated_at,
            "template_used": template_name
        }
        
    except Exception as e:
        logger.error(f"Content generation error: {e}")
        return {"error": str(e)}


@tool
async def create_content_campaign(
    campaign_name: str,
    template_names: List[str],
    topics: List[str],
    target_audiences: Optional[List[str]] = None,
    project_id: str = "tkc-v5-production"
) -> Dict[str, Any]:
    """
    Create a multi-platform content campaign with multiple pieces.
    
    Args:
        campaign_name: Name for the content campaign
        template_names: List of template names to use
        topics: List of topics for each piece of content
        target_audiences: List of target audiences for each piece
        project_id: GCP project ID
    
    Returns:
        Dictionary with campaign results and all generated content
    """
    try:
        campaign_id = f"campaign_{datetime.utcnow().strftime('%Y%m%d_%H%M%S')}"
        generated_content = []
        
        logger.info(f"Starting content campaign: {campaign_name}")
        
        # Ensure we have enough topics and audiences
        if len(topics) < len(template_names):
            topics.extend([topics[0]] * (len(template_names) - len(topics)))
        
        if not target_audiences:
            target_audiences = ["General cryptocurrency users"] * len(template_names)
        elif len(target_audiences) < len(template_names):
            target_audiences.extend([target_audiences[0]] * (len(template_names) - len(target_audiences)))
        
        for i, template_name in enumerate(template_names):
            topic = topics[i]
            audience = target_audiences[i]
            
            # Generate content for this template
            result = await generate_marketing_content.ainvoke({
                "template_name": template_name,
                "topic": topic,
                "key_points": ["Easy to use", "Secure transactions", "Convenient locations"],
                "target_audience": audience,
                "project_id": project_id
            })
            
            if result.get("success"):
                generated_content.append(result)
                logger.info(f"Generated content {i+1}/{len(template_names)}: {result['title'][:50]}...")
            else:
                logger.error(f"Failed to generate content for template: {template_name}")
        
        campaign_result = {
            "campaign_id": campaign_id,
            "campaign_name": campaign_name,
            "total_content_pieces": len(generated_content),
            "successful_generations": len([c for c in generated_content if c.get("success")]),
            "content_pieces": generated_content,
            "created_at": datetime.utcnow().isoformat(),
            "templates_used": template_names,
            "topics_covered": topics[:len(template_names)],
            "target_audiences": target_audiences[:len(template_names)]
        }
        
        logger.info(f"Campaign complete: {len(generated_content)} content pieces generated")
        return campaign_result
        
    except Exception as e:
        logger.error(f"Content campaign creation error: {e}")
        return {"error": str(e)}


@tool
async def ab_test_content_variations(
    template_name: str,
    topic: str,
    key_points: List[str],
    variation_count: int = 2,
    target_audience: str = "General cryptocurrency users",
    project_id: str = "tkc-v5-production"
) -> Dict[str, Any]:
    """
    Generate multiple variations of content for A/B testing.
    
    Args:
        template_name: Name of the content template to use
        topic: Main topic for the content
        key_points: Key points to include
        variation_count: Number of variations to generate
        target_audience: Target audience description
        project_id: GCP project ID
    
    Returns:
        Dictionary with multiple content variations for testing
    """
    try:
        if template_name not in CONTENT_TEMPLATES:
            return {
                "error": f"Template '{template_name}' not found",
                "available_templates": list(CONTENT_TEMPLATES.keys())
            }
        
        variations = []
        
        for i in range(variation_count):
            # Alternate between Gemini and Claude for variety
            use_claude = i % 2 == 1
            
            # Add variation-specific instructions
            variation_instructions = [
                "Focus on emotional appeal and benefits",
                "Emphasize technical features and security",
                "Highlight convenience and ease of use",
                "Stress trustworthiness and reliability"
            ]
            
            custom_instruction = variation_instructions[i % len(variation_instructions)]
            
            result = await generate_marketing_content.ainvoke({
                "template_name": template_name,
                "topic": topic,
                "key_points": key_points,
                "target_audience": target_audience,
                "custom_instructions": custom_instruction,
                "use_claude": use_claude,
                "project_id": project_id
            })
            
            if result.get("success"):
                result["variation_id"] = f"variation_{i+1}"
                result["variation_focus"] = custom_instruction
                variations.append(result)
        
        ab_test_result = {
            "test_id": f"ab_test_{datetime.utcnow().strftime('%Y%m%d_%H%M%S')}",
            "template_used": template_name,
            "topic": topic,
            "target_audience": target_audience,
            "total_variations": len(variations),
            "variations": variations,
            "testing_recommendations": [
                "Test each variation with equal traffic split",
                "Monitor engagement metrics for 7-14 days",
                "Focus on conversion rate as primary metric",
                "Consider audience segmentation for better insights"
            ],
            "created_at": datetime.utcnow().isoformat()
        }
        
        logger.info(f"A/B test content generated: {len(variations)} variations")
        return ab_test_result
        
    except Exception as e:
        logger.error(f"A/B test content generation error: {e}")
        return {"error": str(e)}


@tool
async def list_content_templates() -> Dict[str, Any]:
    """
    List all available content templates with their specifications.
    
    Returns:
        Dictionary with all available templates and their details
    """
    try:
        templates_info = {}
        
        for name, template in CONTENT_TEMPLATES.items():
            templates_info[name] = {
                "name": template.name,
                "content_type": template.content_type.value,
                "platform": template.platform.value,
                "tone": template.tone.value,
                "max_length": template.max_length,
                "structure": template.structure,
                "key_elements": template.key_elements,
                "call_to_action": template.call_to_action,
                "compliance_notes": template.compliance_notes
            }
        
        return {
            "total_templates": len(templates_info),
            "templates": templates_info,
            "content_types": [ct.value for ct in ContentType],
            "platforms": [p.value for p in Platform],
            "tones": [t.value for t in Tone]
        }
        
    except Exception as e:
        logger.error(f"Error listing templates: {e}")
        return {"error": str(e)}
