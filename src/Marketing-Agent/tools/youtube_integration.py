"""
YouTube Data API Integration for Marketing Agent

This module provides comprehensive YouTube API integration for monitoring
cryptocurrency content, identifying influencers, and tracking competitor performance.
"""

import logging
import asyncio
import aiohttp
import json
from typing import List, Dict, Any, Optional
from datetime import datetime, timedelta
from dataclasses import dataclass
from langchain_core.tools import tool

logger = logging.getLogger(__name__)


@dataclass
class YouTubeVideo:
    """Represents a YouTube video with relevant metadata."""
    id: str
    title: str
    description: str
    channel_id: str
    channel_title: str
    published_at: str
    view_count: int
    like_count: int
    comment_count: int
    duration: str
    thumbnail_url: str
    tags: List[str]
    category_id: str


@dataclass
class YouTubeComment:
    """Represents a YouTube comment with relevant metadata."""
    id: str
    text: str
    author_display_name: str
    author_channel_id: str
    like_count: int
    published_at: str
    updated_at: str
    video_id: str
    parent_id: Optional[str] = None


@dataclass
class YouTubeChannel:
    """Represents a YouTube channel with relevant metadata."""
    id: str
    title: str
    description: str
    subscriber_count: int
    video_count: int
    view_count: int
    published_at: str
    thumbnail_url: str
    country: Optional[str] = None


class YouTubeAPIClient:
    """
    YouTube Data API v3 client for content monitoring and analysis.
    
    Requires a Google Cloud API key with YouTube Data API v3 enabled.
    """
    
    def __init__(self, api_key: str):
        self.api_key = api_key
        self.base_url = "https://www.googleapis.com/youtube/v3"
        self.session = None
        
    async def __aenter__(self):
        """Async context manager entry."""
        self.session = aiohttp.ClientSession(
            timeout=aiohttp.ClientTimeout(total=30)
        )
        return self
        
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit."""
        if self.session:
            await self.session.close()
    
    async def search_videos(
        self,
        query: str,
        max_results: int = 25,
        order: str = "relevance",
        published_after: Optional[str] = None,
        published_before: Optional[str] = None,
        video_duration: str = "any"
    ) -> List[YouTubeVideo]:
        """
        Search for videos based on query.
        
        Args:
            query: Search query string
            max_results: Maximum number of results (1-50)
            order: Sort order (date, rating, relevance, title, viewCount)
            published_after: RFC 3339 formatted date-time
            published_before: RFC 3339 formatted date-time
            video_duration: any, long (>20min), medium (4-20min), short (<4min)
        """
        try:
            url = f"{self.base_url}/search"
            params = {
                "part": "snippet",
                "q": query,
                "type": "video",
                "maxResults": min(max_results, 50),
                "order": order,
                "videoDuration": video_duration,
                "key": self.api_key
            }
            
            if published_after:
                params["publishedAfter"] = published_after
            if published_before:
                params["publishedBefore"] = published_before
            
            async with self.session.get(url, params=params) as response:
                if response.status == 200:
                    data = await response.json()
                    video_ids = [item["id"]["videoId"] for item in data.get("items", [])]
                    
                    # Get detailed video statistics
                    if video_ids:
                        return await self.get_video_details(video_ids)
                    return []
                else:
                    logger.error(f"YouTube API error: {response.status}")
                    return []
                    
        except Exception as e:
            logger.error(f"Error searching YouTube videos: {e}")
            return []
    
    async def get_video_details(self, video_ids: List[str]) -> List[YouTubeVideo]:
        """Get detailed information for specific videos."""
        try:
            url = f"{self.base_url}/videos"
            params = {
                "part": "snippet,statistics,contentDetails",
                "id": ",".join(video_ids),
                "key": self.api_key
            }
            
            async with self.session.get(url, params=params) as response:
                if response.status == 200:
                    data = await response.json()
                    videos = []
                    
                    for item in data.get("items", []):
                        snippet = item.get("snippet", {})
                        statistics = item.get("statistics", {})
                        content_details = item.get("contentDetails", {})
                        
                        videos.append(YouTubeVideo(
                            id=item.get("id"),
                            title=snippet.get("title", ""),
                            description=snippet.get("description", ""),
                            channel_id=snippet.get("channelId", ""),
                            channel_title=snippet.get("channelTitle", ""),
                            published_at=snippet.get("publishedAt", ""),
                            view_count=int(statistics.get("viewCount", 0)),
                            like_count=int(statistics.get("likeCount", 0)),
                            comment_count=int(statistics.get("commentCount", 0)),
                            duration=content_details.get("duration", ""),
                            thumbnail_url=snippet.get("thumbnails", {}).get("medium", {}).get("url", ""),
                            tags=snippet.get("tags", []),
                            category_id=snippet.get("categoryId", "")
                        ))
                    
                    logger.info(f"Retrieved details for {len(videos)} videos")
                    return videos
                else:
                    logger.error(f"YouTube API error: {response.status}")
                    return []
                    
        except Exception as e:
            logger.error(f"Error getting video details: {e}")
            return []
    
    async def get_video_comments(
        self,
        video_id: str,
        max_results: int = 50,
        order: str = "relevance"
    ) -> List[YouTubeComment]:
        """Get comments for a specific video."""
        try:
            url = f"{self.base_url}/commentThreads"
            params = {
                "part": "snippet",
                "videoId": video_id,
                "maxResults": min(max_results, 100),
                "order": order,
                "textFormat": "plainText",
                "key": self.api_key
            }
            
            async with self.session.get(url, params=params) as response:
                if response.status == 200:
                    data = await response.json()
                    comments = []
                    
                    for item in data.get("items", []):
                        comment_data = item.get("snippet", {}).get("topLevelComment", {}).get("snippet", {})
                        
                        comments.append(YouTubeComment(
                            id=item.get("snippet", {}).get("topLevelComment", {}).get("id", ""),
                            text=comment_data.get("textDisplay", ""),
                            author_display_name=comment_data.get("authorDisplayName", ""),
                            author_channel_id=comment_data.get("authorChannelId", {}).get("value", ""),
                            like_count=int(comment_data.get("likeCount", 0)),
                            published_at=comment_data.get("publishedAt", ""),
                            updated_at=comment_data.get("updatedAt", ""),
                            video_id=video_id
                        ))
                    
                    logger.info(f"Retrieved {len(comments)} comments for video {video_id}")
                    return comments
                else:
                    logger.error(f"YouTube API error: {response.status}")
                    return []
                    
        except Exception as e:
            logger.error(f"Error getting video comments: {e}")
            return []
    
    async def get_channel_details(self, channel_ids: List[str]) -> List[YouTubeChannel]:
        """Get detailed information for specific channels."""
        try:
            url = f"{self.base_url}/channels"
            params = {
                "part": "snippet,statistics",
                "id": ",".join(channel_ids),
                "key": self.api_key
            }
            
            async with self.session.get(url, params=params) as response:
                if response.status == 200:
                    data = await response.json()
                    channels = []
                    
                    for item in data.get("items", []):
                        snippet = item.get("snippet", {})
                        statistics = item.get("statistics", {})
                        
                        channels.append(YouTubeChannel(
                            id=item.get("id"),
                            title=snippet.get("title", ""),
                            description=snippet.get("description", ""),
                            subscriber_count=int(statistics.get("subscriberCount", 0)),
                            video_count=int(statistics.get("videoCount", 0)),
                            view_count=int(statistics.get("viewCount", 0)),
                            published_at=snippet.get("publishedAt", ""),
                            thumbnail_url=snippet.get("thumbnails", {}).get("medium", {}).get("url", ""),
                            country=snippet.get("country")
                        ))
                    
                    logger.info(f"Retrieved details for {len(channels)} channels")
                    return channels
                else:
                    logger.error(f"YouTube API error: {response.status}")
                    return []
                    
        except Exception as e:
            logger.error(f"Error getting channel details: {e}")
            return []


# Cryptocurrency and Bitcoin ATM related keywords for YouTube monitoring
CRYPTO_YOUTUBE_KEYWORDS = [
    "bitcoin atm",
    "crypto atm",
    "coinme",
    "bitcoin machine",
    "buy bitcoin",
    "bitcoin tutorial",
    "cryptocurrency explained",
    "bitcoin for beginners",
    "bitcoin depot",
    "coinstar bitcoin",
    "bitcoin fees",
    "crypto investment"
]

# Competitor channels to monitor
COMPETITOR_CHANNELS = {
    "Bitcoin Depot": "UCxxxxxxxxxxxxxxx",  # Would need actual channel IDs
    "Coinstar": "UCxxxxxxxxxxxxxxx",
    "MoneyGram": "UCxxxxxxxxxxxxxxx"
}


@tool
async def monitor_youtube_content(
    keywords: List[str] = None,
    max_videos_per_keyword: int = 10,
    time_filter: str = "week",
    api_key: str = None
) -> Dict[str, Any]:
    """
    Monitor YouTube content for cryptocurrency and Bitcoin ATM discussions.
    
    Args:
        keywords: List of keywords to search for
        max_videos_per_keyword: Maximum videos to analyze per keyword
        time_filter: Time filter (day, week, month)
        api_key: YouTube Data API key
    
    Returns:
        Dictionary with found videos and analysis
    """
    try:
        if not api_key:
            # In production, this would come from Secret Manager
            logger.warning("YouTube API key not provided - using mock data")
            return _generate_mock_youtube_data(keywords or CRYPTO_YOUTUBE_KEYWORDS[:3])
        
        if keywords is None:
            keywords = CRYPTO_YOUTUBE_KEYWORDS[:5]
        
        # Calculate time filter
        time_filters = {
            "day": datetime.utcnow() - timedelta(days=1),
            "week": datetime.utcnow() - timedelta(weeks=1),
            "month": datetime.utcnow() - timedelta(days=30)
        }
        published_after = time_filters.get(time_filter, time_filters["week"]).isoformat() + "Z"
        
        all_videos = []
        
        async with YouTubeAPIClient(api_key) as youtube:
            for keyword in keywords:
                videos = await youtube.search_videos(
                    query=keyword,
                    max_results=max_videos_per_keyword,
                    order="relevance",
                    published_after=published_after
                )
                all_videos.extend(videos)
        
        # Analyze videos for engagement opportunities
        opportunities = []
        influencers = []
        
        for video in all_videos:
            # Calculate engagement rate
            engagement_rate = 0
            if video.view_count > 0:
                engagement_rate = (video.like_count + video.comment_count) / video.view_count * 100
            
            # Identify potential opportunities
            opportunity_score = 0
            opportunity_reasons = []
            
            content_text = (video.title + " " + video.description).lower()
            
            if any(word in content_text for word in ["fee", "expensive", "cost"]):
                opportunity_score += 3
                opportunity_reasons.append("fee_discussion")
            
            if any(word in content_text for word in ["scam", "fraud", "avoid"]):
                opportunity_score += 4
                opportunity_reasons.append("scam_concern")
            
            if any(word in content_text for word in ["tutorial", "how to", "guide"]):
                opportunity_score += 2
                opportunity_reasons.append("educational_content")
            
            if any(word in content_text for word in ["coinme", "bitcoin depot", "coinstar"]):
                opportunity_score += 5
                opportunity_reasons.append("competitor_mention")
            
            # High engagement videos are good for commenting
            if engagement_rate > 2.0:
                opportunity_score += 2
                opportunity_reasons.append("high_engagement")
            
            if opportunity_score >= 3:
                opportunities.append({
                    "video_id": video.id,
                    "title": video.title,
                    "channel_title": video.channel_title,
                    "view_count": video.view_count,
                    "comment_count": video.comment_count,
                    "engagement_rate": round(engagement_rate, 2),
                    "opportunity_score": opportunity_score,
                    "opportunity_reasons": opportunity_reasons,
                    "published_at": video.published_at,
                    "url": f"https://www.youtube.com/watch?v={video.id}"
                })
            
            # Identify potential influencers (high subscriber channels)
            if video.view_count > 10000:  # Significant reach
                influencers.append({
                    "channel_id": video.channel_id,
                    "channel_title": video.channel_title,
                    "video_title": video.title,
                    "view_count": video.view_count,
                    "engagement_rate": round(engagement_rate, 2)
                })
        
        # Sort opportunities by score
        opportunities.sort(key=lambda x: x["opportunity_score"], reverse=True)
        
        # Remove duplicate influencers
        unique_influencers = {inf["channel_id"]: inf for inf in influencers}.values()
        
        result = {
            "total_videos_analyzed": len(all_videos),
            "engagement_opportunities": len(opportunities),
            "potential_influencers": len(unique_influencers),
            "top_opportunities": opportunities[:10],
            "top_influencers": sorted(unique_influencers, key=lambda x: x["view_count"], reverse=True)[:5],
            "keywords_searched": keywords,
            "time_filter": time_filter,
            "timestamp": datetime.utcnow().isoformat()
        }
        
        logger.info(f"YouTube monitoring complete: {len(opportunities)} opportunities found")
        return result
        
    except Exception as e:
        logger.error(f"YouTube monitoring error: {e}")
        return {"error": str(e), "opportunities": []}


def _generate_mock_youtube_data(keywords: List[str]) -> Dict[str, Any]:
    """Generate mock YouTube data for testing when API key is not available."""
    mock_opportunities = [
        {
            "video_id": "mock_video_1",
            "title": "Bitcoin ATM Fees Are Too High! Here's Why",
            "channel_title": "CryptoTruth",
            "view_count": 15420,
            "comment_count": 89,
            "engagement_rate": 2.3,
            "opportunity_score": 7,
            "opportunity_reasons": ["fee_discussion", "high_engagement"],
            "published_at": "2024-01-15T10:30:00Z",
            "url": "https://www.youtube.com/watch?v=mock_video_1"
        },
        {
            "video_id": "mock_video_2", 
            "title": "How to Use Coinme Bitcoin ATM - Complete Tutorial",
            "channel_title": "BitcoinBeginners",
            "view_count": 8750,
            "comment_count": 45,
            "engagement_rate": 1.8,
            "opportunity_score": 6,
            "opportunity_reasons": ["competitor_mention", "educational_content"],
            "published_at": "2024-01-14T14:20:00Z",
            "url": "https://www.youtube.com/watch?v=mock_video_2"
        }
    ]
    
    mock_influencers = [
        {
            "channel_id": "mock_channel_1",
            "channel_title": "CryptoTruth",
            "video_title": "Bitcoin ATM Fees Are Too High! Here's Why",
            "view_count": 15420,
            "engagement_rate": 2.3
        }
    ]
    
    return {
        "total_videos_analyzed": 25,
        "engagement_opportunities": 2,
        "potential_influencers": 1,
        "top_opportunities": mock_opportunities,
        "top_influencers": mock_influencers,
        "keywords_searched": keywords,
        "time_filter": "week",
        "timestamp": datetime.utcnow().isoformat(),
        "note": "Mock data - YouTube API key required for live data"
    }


@tool
async def analyze_youtube_video_engagement(
    video_id: str,
    api_key: str = None
) -> Dict[str, Any]:
    """
    Analyze a specific YouTube video for engagement opportunities.
    
    Args:
        video_id: YouTube video ID
        api_key: YouTube Data API key
    
    Returns:
        Analysis of engagement opportunity and recommended approach
    """
    try:
        if not api_key:
            logger.warning("YouTube API key not provided - using mock analysis")
            return {
                "video_id": video_id,
                "engagement_potential": "high",
                "recommended_approach": "educational_comment",
                "key_themes": ["fee_concern", "educational_content"],
                "comment_strategy": "Provide helpful information about Bitcoin ATM alternatives",
                "risk_level": "low",
                "note": "Mock analysis - YouTube API key required for live analysis"
            }
        
        async with YouTubeAPIClient(api_key) as youtube:
            # Get video details
            videos = await youtube.get_video_details([video_id])
            if not videos:
                return {"error": "Video not found"}
            
            video = videos[0]
            
            # Get comments for context
            comments = await youtube.get_video_comments(video_id, max_results=20)
            
            # Analyze content
            content_text = (video.title + " " + video.description).lower()
            
            analysis = {
                "video_id": video_id,
                "title": video.title,
                "channel_title": video.channel_title,
                "view_count": video.view_count,
                "comment_count": video.comment_count,
                "engagement_potential": "medium",
                "recommended_approach": "helpful_comment",
                "key_themes": [],
                "comment_strategy": "",
                "risk_level": "low"
            }
            
            # Identify themes and determine approach
            if any(word in content_text for word in ["fee", "expensive", "cost"]):
                analysis["key_themes"].append("fee_concern")
                analysis["comment_strategy"] = "fee_transparency"
                analysis["engagement_potential"] = "high"
            
            if any(word in content_text for word in ["scam", "fraud", "avoid"]):
                analysis["key_themes"].append("scam_concern")
                analysis["comment_strategy"] = "trust_building"
                analysis["engagement_potential"] = "high"
                analysis["risk_level"] = "medium"
            
            if any(word in content_text for word in ["tutorial", "how", "guide"]):
                analysis["key_themes"].append("educational_content")
                analysis["comment_strategy"] = "educational_supplement"
                analysis["engagement_potential"] = "high"
            
            if any(word in content_text for word in ["coinme", "bitcoin depot", "coinstar"]):
                analysis["key_themes"].append("competitor_mention")
                analysis["comment_strategy"] = "competitive_differentiation"
                analysis["engagement_potential"] = "medium"
            
            # Analyze comment sentiment
            if comments:
                negative_comments = sum(1 for c in comments if any(word in c.text.lower() for word in ["bad", "terrible", "scam", "avoid"]))
                if negative_comments > len(comments) * 0.3:
                    analysis["risk_level"] = "high"
            
            analysis["total_comments_analyzed"] = len(comments)
            
            logger.info(f"Analyzed YouTube video {video_id}: {analysis['engagement_potential']} potential")
            return analysis
            
    except Exception as e:
        logger.error(f"YouTube video analysis error: {e}")
        return {"error": str(e)}
